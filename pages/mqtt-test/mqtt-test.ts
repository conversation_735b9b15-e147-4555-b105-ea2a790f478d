import { mqttManager } from '../../services/mqttManager';
import type { DevicePropertyData, DeviceEventData } from '../../services/mqtt';

Page({
  data: {
    mqttStatus: {
      connected: false,
      connecting: false,
      error: null as string | null,
      retryCount: 0,
      subscribedTopics: [] as string[],
    },
    messages: [] as Array<{
      id: string;
      type: 'property' | 'event';
      deviceId: string;
      timestamp: string;
      data: any;
    }>,
    testDeviceId: 'test_device_001',
  },

  onLoad() {
    console.log('MQTT测试页面加载');
    this.updateMqttStatus();
    this.setupGlobalListeners();
  },

  onUnload() {
    // 清理监听器
    this.removeGlobalListeners();
  },

  /**
   * 更新MQTT状态
   */
  updateMqttStatus() {
    const stats = mqttManager.getConnectionStats();
    this.setData({
      mqttStatus: {
        connected: stats.isConnected,
        connecting: false,
        error: null,
        retryCount: stats.retryCount,
        subscribedTopics: stats.subscribedTopics,
      },
    });
  },

  /**
   * 设置全局监听器
   */
  setupGlobalListeners() {
    // 监听全局设备属性更新
    mqttManager.onGlobalPropertyUpdate((data: DevicePropertyData) => {
      this.addMessage('property', data.deviceId, data);
    });

    // 监听全局设备事件
    mqttManager.onGlobalEvent((data: DeviceEventData) => {
      this.addMessage('event', data.deviceId, data);
    });
  },

  /**
   * 移除全局监听器
   */
  removeGlobalListeners() {
    // 这里应该移除监听器，但由于eventBus的限制，我们暂时不实现
    // 在实际项目中，可以考虑改进eventBus来支持移除特定监听器
  },

  /**
   * 添加消息到列表
   */
  addMessage(type: 'property' | 'event', deviceId: string, data: any) {
    const message = {
      id: Date.now().toString(),
      type,
      deviceId,
      timestamp: new Date().toLocaleString(),
      data,
    };

    const messages = [message, ...this.data.messages.slice(0, 49)]; // 保留最新50条
    this.setData({ messages });
  },

  /**
   * 手动连接MQTT
   */
  async onConnectMqtt() {
    try {
      this.setData({
        'mqttStatus.connecting': true,
        'mqttStatus.error': null,
      });

      await mqttManager.manualReconnect();
      this.updateMqttStatus();

      wx.showToast({
        title: 'MQTT连接成功',
        icon: 'success',
      });
    } catch (error) {
      console.error('MQTT连接失败:', error);
      this.setData({
        'mqttStatus.connecting': false,
        'mqttStatus.error': '连接失败',
      });

      wx.showToast({
        title: 'MQTT连接失败',
        icon: 'none',
      });
    }
  },

  /**
   * 断开MQTT连接
   */
  onDisconnectMqtt() {
    mqttManager.destroy();
    this.updateMqttStatus();
    wx.showToast({
      title: 'MQTT已断开',
      icon: 'success',
    });
  },

  /**
   * 清空消息列表
   */
  onClearMessages() {
    this.setData({ messages: [] });
  },

  /**
   * 模拟发送测试消息
   */
  onSendTestMessage() {
    // 模拟属性更新消息
    const propertyData: DevicePropertyData = {
      deviceId: this.data.testDeviceId,
      properties: {
        batteryLevel: Math.floor(Math.random() * 100),
        batteryStatus: Math.random() > 0.5 ? '充电中' : '放电中',
        voltages: Array.from({ length: 8 }, () => 3400 + Math.floor(Math.random() * 200)),
        temperatures: Array.from({ length: 4 }, () => 20 + Math.floor(Math.random() * 20)),
      },
      timestamp: Date.now(),
    };

    this.addMessage('property', propertyData.deviceId, propertyData);

    // 模拟事件消息
    setTimeout(() => {
      const eventData: DeviceEventData = {
        deviceId: this.data.testDeviceId,
        eventType: Math.random() > 0.7 ? 'alarm' : 'status_change',
        eventData: {
          message: '这是一个测试事件',
          level: Math.random() > 0.5 ? 'warning' : 'info',
        },
        timestamp: Date.now(),
      };

      this.addMessage('event', eventData.deviceId, eventData);
    }, 1000);
  },

  /**
   * 查看消息详情
   */
  onViewMessage(event: any) {
    const messageId = event.currentTarget.dataset.id;
    const message = this.data.messages.find(m => m.id === messageId);
    
    if (message) {
      wx.showModal({
        title: `${message.type === 'property' ? '属性' : '事件'}消息详情`,
        content: JSON.stringify(message.data, null, 2),
        showCancel: false,
      });
    }
  },

  /**
   * 刷新状态
   */
  onRefreshStatus() {
    this.updateMqttStatus();
    wx.showToast({
      title: '状态已刷新',
      icon: 'success',
    });
  },
});
