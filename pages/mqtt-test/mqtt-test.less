/* pages/mqtt-test/mqtt-test.less */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.status-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .status-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .refresh-btn {
      background: #007aff;
      color: white;
      border: none;
    }
  }
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .status-label {
      font-size: 28rpx;
      color: #666;
    }
    
    .status-value {
      font-size: 28rpx;
      font-weight: 500;
      
      &.connected {
        color: #34c759;
      }
      
      &.disconnected {
        color: #ff3b30;
      }
      
      &.error {
        color: #ff3b30;
      }
    }
  }
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
  
  .action-btn {
    height: 80rpx;
    border-radius: 12rpx;
    font-size: 28rpx;
    font-weight: 500;
    
    &.connect-btn {
      background: #34c759;
      color: white;
      
      &[disabled] {
        background: #ccc;
      }
    }
    
    &.disconnect-btn {
      background: #ff3b30;
      color: white;
      
      &[disabled] {
        background: #ccc;
      }
    }
    
    &.test-btn {
      background: #007aff;
      color: white;
    }
    
    &.clear-btn {
      background: #ff9500;
      color: white;
    }
  }
}

.messages-section {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .messages-header {
    padding: 30rpx;
    border-bottom: 1rpx solid #eee;
    
    .messages-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .messages-list {
    height: 600rpx;
    
    .message-item {
      padding: 24rpx 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.property {
        border-left: 6rpx solid #34c759;
      }
      
      &.event {
        border-left: 6rpx solid #ff9500;
      }
      
      .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;
        
        .message-type {
          font-size: 24rpx;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          color: white;
          
          .property & {
            background: #34c759;
          }
          
          .event & {
            background: #ff9500;
          }
        }
        
        .message-time {
          font-size: 22rpx;
          color: #999;
        }
      }
      
      .message-content {
        .message-device {
          display: block;
          font-size: 26rpx;
          color: #666;
          margin-bottom: 8rpx;
        }
        
        .message-preview {
          display: block;
          font-size: 28rpx;
          color: #333;
        }
      }
    }
    
    .empty-message {
      padding: 60rpx;
      text-align: center;
      color: #999;
      font-size: 28rpx;
    }
  }
}
