<!--pages/mqtt-test/mqtt-test.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">MQTT 功能测试</text>
  </view>

  <!-- MQTT状态卡片 -->
  <view class="status-card">
    <view class="status-header">
      <text class="status-title">连接状态</text>
      <button class="refresh-btn" size="mini" bindtap="onRefreshStatus">刷新</button>
    </view>
    
    <view class="status-item">
      <text class="status-label">连接状态:</text>
      <text class="status-value {{mqttStatus.connected ? 'connected' : 'disconnected'}}">
        {{mqttStatus.connected ? '已连接' : '未连接'}}
      </text>
    </view>
    
    <view class="status-item" wx:if="{{mqttStatus.error}}">
      <text class="status-label">错误信息:</text>
      <text class="status-value error">{{mqttStatus.error}}</text>
    </view>
    
    <view class="status-item">
      <text class="status-label">重试次数:</text>
      <text class="status-value">{{mqttStatus.retryCount}}</text>
    </view>
    
    <view class="status-item">
      <text class="status-label">订阅主题:</text>
      <text class="status-value">{{mqttStatus.subscribedTopics.length}}个</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button 
      class="action-btn connect-btn" 
      bindtap="onConnectMqtt"
      disabled="{{mqttStatus.connecting || mqttStatus.connected}}"
    >
      {{mqttStatus.connecting ? '连接中...' : '连接MQTT'}}
    </button>
    
    <button 
      class="action-btn disconnect-btn" 
      bindtap="onDisconnectMqtt"
      disabled="{{!mqttStatus.connected}}"
    >
      断开连接
    </button>
    
    <button 
      class="action-btn test-btn" 
      bindtap="onSendTestMessage"
    >
      发送测试消息
    </button>
    
    <button 
      class="action-btn clear-btn" 
      bindtap="onClearMessages"
    >
      清空消息
    </button>
  </view>

  <!-- 消息列表 -->
  <view class="messages-section">
    <view class="messages-header">
      <text class="messages-title">接收到的消息 ({{messages.length}})</text>
    </view>
    
    <scroll-view class="messages-list" scroll-y="true">
      <view 
        class="message-item {{item.type}}" 
        wx:for="{{messages}}" 
        wx:key="id"
        bindtap="onViewMessage"
        data-id="{{item.id}}"
      >
        <view class="message-header">
          <text class="message-type">{{item.type === 'property' ? '属性' : '事件'}}</text>
          <text class="message-time">{{item.timestamp}}</text>
        </view>
        
        <view class="message-content">
          <text class="message-device">设备: {{item.deviceId}}</text>
          <text class="message-preview">
            {{item.type === 'property' ? '属性更新' : item.data.eventType || '事件'}}
          </text>
        </view>
      </view>
      
      <view class="empty-message" wx:if="{{messages.length === 0}}">
        <text>暂无消息</text>
      </view>
    </scroll-view>
  </view>
</view>
