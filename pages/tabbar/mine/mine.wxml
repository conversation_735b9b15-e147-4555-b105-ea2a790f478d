<!--pages/mine/mine.wxml-->
<t-navbar title="个人中心" />

<scroll-view class="page-container" scroll-y type="list">
  <view class="content">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-info-container">
        <view class="user-avatar-container">
          <view class="user-avatar" wx:if="{{isLoggedIn}}" bindtap="onAvatarTap">
            <image src="{{userInfo.avatar || '/images/default.svg'}}" mode="aspectFill" />
            <view class="avatar-mask">
              <view class="upload-text">点击上传</view>
            </view>
          </view>
          <view class="user-avatar default" wx:else>
            <view class="avatar-icon">
              <image src="/images/default.svg" mode="aspectFit" />
            </view>
          </view>
        </view>
        <view class="user-details">
          <view class="user-name-section" wx:if="{{isLoggedIn}}">
            <view class="user-name">{{userInfo.nickname || '未设置昵称'}}</view>
            <view class="user-phone-container" wx:if="{{userInfo.mobile}}">
              <view class="phone-icon">
                <image src="/images/mobile.svg" mode="aspectFit" />
              </view>
              <view class="phone-number">{{userInfo.mobile}}</view>
            </view>
          </view>
          <view class="login-prompt" wx:else>
            <view class="prompt-text" bindtap="onLogin">请先登录/注册</view>
          </view>
          <view class="edit-section" wx:if="{{isLoggedIn}}" bindtap="onEditProfile">
            <view class="edit-icon">
              <image src="/images/edit.svg" mode="aspectFit" />
            </view>
            <view class="edit-text">编辑</view>
          </view>
        </view>
      </view>

      <!-- 分割线 -->
      <view class="divider"></view>

      <!-- 充电记录 -->
      <view class="charging-record-section" bindtap="onChargingRecord">
        <view class="record-icon">
          <image src="/images/recharging.svg" mode="aspectFit" />
        </view>
        <view class="record-text">充电记录</view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" bindtap="onMenuTap" data-type="contact">
        <view class="menu-icon">
          <image src="/images/service.svg" mode="aspectFit" />
        </view>
        <view class="menu-text">联系我们</view>
        <view class="menu-arrow">
          <image src="/images/chevron-right.svg" mode="aspectFit" />
        </view>
      </view>

      <view class="menu-item" bindtap="onMenuTap" data-type="help">
        <view class="menu-icon">
          <image src="/images/file.svg" mode="aspectFit" />
        </view>
        <view class="menu-text">使用说明</view>
        <view class="menu-arrow">
          <image src="/images/chevron-right.svg" mode="aspectFit" />
        </view>
      </view>

      <!-- 退出登录按钮 -->
      <view class="menu-item logout-item" wx:if="{{isLoggedIn}}" bindtap="onLogout">
        <view class="menu-icon">
          <image src="/images/shutdown.svg" mode="aspectFit" />
        </view>
        <view class="menu-text logout-text">退出登录</view>
        <view class="menu-arrow">
          <image src="/images/chevron-right.svg" mode="aspectFit" />
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</scroll-view>