// index.ts
// 获取应用实例
const app = getApp<IAppOption>();
import { onPageShow } from '../../../utils/tabbar.js';
import { getDeviceBySn } from '../../../api/device';

// 定义扫码数据类型
interface ScanData {
  sn: string;
  [key: string]: any;
}

Page({
  data: {
    // 设备基本信息
    deviceInfo: {
      name: '设备001',
      status: '在线·低电',
      batteryLevel: 30,
      voltage: '28.2V',
      current: '0.0A',
      power: '0.2W',
      alertStatus: '总体过压报警',
      batteryStatus: 'low',
    },

    // 地图相关数据
    longitude: 116.397128,
    latitude: 39.916527,
    markers: [
      {
        id: 1,
        latitude: 39.916527,
        longitude: 116.397128,
        title: '设备位置',
        // iconPath: '/images/location-marker.png',
        width: 30,
        height: 30,
      },
    ],

    // 扫码确认弹窗相关
    showScanConfirmPopup: false,
    scannedDeviceInfo: {
      serialNumber: '',
      firmwareVersion: '',
      hardwareVersion: '',
      rawScanData: '',
    },
  },

  onLoad() {
    this.loadDeviceData();
    this.getCurrentLocation();
    this.checkBatteryStatus();
  },

  onShow() {
    // 使用工具函数更新自定义tabbar状态
    onPageShow(this);
  },

  // 加载设备数据
  loadDeviceData() {
    // 模拟从API获取设备数据
    // 在实际项目中，这里应该调用真实的API
    console.log('加载设备数据');

    // 可以在这里添加数据刷新逻辑
    this.refreshDeviceStatus();
  },

  // 刷新设备状态
  refreshDeviceStatus() {
    // 模拟实时数据更新
    const currentTime = new Date().getTime();
    const randomVoltage = (28.0 + Math.random() * 0.5).toFixed(1);
    const randomCurrent = (Math.random() * 0.1).toFixed(1);
    const randomPower = (
      (parseFloat(randomVoltage) * parseFloat(randomCurrent)) /
      1000
    ).toFixed(1);

    this.setData({
      'deviceInfo.voltage': `${randomVoltage}V`,
      'deviceInfo.current': `${randomCurrent}A`,
      'deviceInfo.power': `${randomPower}W`,
      'deviceInfo.lastUpdate': currentTime,
    });
  },

  // 获取当前位置
  getCurrentLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('获取位置成功:', res);
        this.setData({
          longitude: res.longitude,
          latitude: res.latitude,
          'markers[0].longitude': res.longitude,
          'markers[0].latitude': res.latitude,
        });
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        wx.showToast({
          title: '获取位置失败',
          icon: 'none',
        });
      },
    });
  },

  // 强制启动
  onForceStart() {
    wx.showModal({
      title: '确认操作',
      content: '确定要强制启动设备吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDeviceAction('强制启动');
        }
      },
    });
  },

  // 预约加热
  onScheduleHeat() {
    wx.navigateTo({
      url: '/pages/schedule-heat/schedule-heat',
    });
  },

  // 远程开机
  onRemoteStart() {
    wx.showModal({
      title: '确认操作',
      content: '确定要远程开机吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDeviceAction('远程开机');
        }
      },
    });
  },

  // 远程关机
  onRemoteShutdown() {
    wx.showModal({
      title: '确认操作',
      content: '确定要远程关机吗？此操作可能会影响设备正常运行。',
      success: (res) => {
        if (res.confirm) {
          this.performDeviceAction('远程关机');
        }
      },
    });
  },

  // 蓝牙搜索
  onBluetoothSearch() {
    wx.navigateTo({
      url: '/pages/bluetooth-search/bluetooth-search',
    });
  },

  // 执行设备操作
  performDeviceAction(action: string) {
    wx.showLoading({
      title: '执行中...',
      mask: true,
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: `${action}成功`,
        icon: 'success',
      });

      // 刷新设备状态
      this.refreshDeviceStatus();
    }, 2000);
  },

  // 地图点击事件
  onMapTap() {
    wx.openLocation({
      latitude: this.data.latitude,
      longitude: this.data.longitude,
      name: '设备位置',
      address: '设备详细地址',
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDeviceData();
    this.getCurrentLocation();

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '设备监控 - 实时查看设备状态',
      path: '/pages/index/index',
      imageUrl: '/images/share-image.jpg',
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '设备监控 - 智能设备管理',
      query: '',
      imageUrl: '/images/share-timeline.jpg',
    };
  },

  // 检查电池状态
  checkBatteryStatus() {
    const batteryLevel = this.data.deviceInfo.batteryLevel;
    let batteryStatus = 'normal';
    let status = '在线·正常';

    if (batteryLevel <= 20) {
      batteryStatus = 'low';
      status = '在线·低电';
    }

    this.setData({
      'deviceInfo.batteryStatus': batteryStatus,
      'deviceInfo.status': status,
    });
  },

  // 模拟切换电池状态（用于测试）
  toggleBatteryStatus() {
    const currentStatus = this.data.deviceInfo.batteryStatus;
    const newStatus = currentStatus === 'normal' ? 'low' : 'normal';
    const newLevel = newStatus === 'low' ? 16 : 86;
    const newStatusText = newStatus === 'low' ? '在线·低电' : '在线·正常';

    this.setData({
      'deviceInfo.batteryStatus': newStatus,
      'deviceInfo.batteryLevel': newLevel,
      'deviceInfo.status': newStatusText,
    });
  },

  onScan() {
    wx.scanCode({
      onlyFromCamera: true,
      success: (res) => {
        console.log('扫码结果:', res);
        const scannedData = res.result;
        console.log('扫码数据:', scannedData);

        // 模拟设备识别过程
        this.recognizeDevice(scannedData);
      },
      fail: (err) => {
        console.error('扫码失败:', err);
        // wx.showToast({
        //   title: '扫码失败',
        //   icon: 'none',
        // });
      },
    });
  },

  // 设备识别
  async recognizeDevice(scanData: string) {
    wx.showLoading({
      title: '识别设备中...',
      mask: true,
    });

    try {
      // 解析扫码数据
      const parsedScanData: ScanData = JSON.parse(scanData);

      // 通过API验证设备是否存在
      const res = await getDeviceBySn(parsedScanData.sn);
      console.log('设备信息:', res);

      if (res) {
        // 设备识别成功
        const deviceInfo = {
          serialNumber: parsedScanData.sn,
          firmwareVersion: res?.properties?.FirmwareVersion
            ? `V ${res.properties.FirmwareVersion}`
            : '',
          hardwareVersion: res?.properties?.FirmwareVersion
            ? `V ${res.properties.FirmwareVersion}`
            : '',
          rawScanData: JSON.stringify(parsedScanData),
        };

        // 更新数据并显示确认弹窗
        this.setData({
          scannedDeviceInfo: deviceInfo,
          showScanConfirmPopup: true,
        });
      } else {
        // 设备不存在或获取失败
        throw new Error(res.message || '设备不存在');
      }
    } catch (error: any) {
      console.error('设备识别失败:', error);
      wx.showModal({
        title: '设备识别失败',
        content:
          error.message || '未能识别该设备，请检查二维码是否为有效的设备码',
        showCancel: false,
        confirmText: '确定',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 确认设备 - 跳转到设备信息页面
  onConfirmDevice() {
    const { scannedDeviceInfo } = this.data;

    this.setData({
      showScanConfirmPopup: false,
    });

    // 跳转到扫码设备信息页面，让用户进行绑定操作
    wx.navigateTo({
      url: `/pages/scan-device-info/scan-device-info?deviceId=${scannedDeviceInfo.serialNumber}`,
    });
  },

  // 取消确认
  onCancelDevice() {
    this.setData({
      showScanConfirmPopup: false,
    });
  },

  // 弹窗可见性变化
  onScanPopupVisibleChange(e: any) {
    this.setData({
      showScanConfirmPopup: e.detail.visible,
    });
  },

  onShowDeviceList() {
    wx.navigateTo({
      url: '/pages/device-list/device-list',
    });
  },

  onShowMoreInfo() {
    wx.navigateTo({
      url:
        '/pages/tabbar/detail/detail?sn=' +
        this.data.scannedDeviceInfo?.serialNumber,
    });
  },

  // 解绑设备
  onUnbindDevice() {
    wx.showModal({
      title: '确认解绑',
      content: '确定要解绑当前设备吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '解绑成功',
            icon: 'success',
          });
          // TODO: 实现解绑逻辑
        }
      },
    });
  },
});
