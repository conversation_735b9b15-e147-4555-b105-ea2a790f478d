<!--index.wxml-->
<t-navbar t-class-placeholder="t-navbar-placeholder" t-class-content="t-navbar-content" title="设备">
  <!-- <view slot="left">
    <image src="/images/scan.png" class="navbar-icon" bindtap="onScan" />
  </view> -->
  <!-- <view slot="left">
    <text class="navbar-icon" bindtap="onShowDeviceList">设备列表</text>
  </view> -->
</t-navbar>
<!-- 蓝牙入口图标 -->
<view class="bluetooth-entry" bindtap="onBluetoothSearch">
  <view class="bluetooth-icon">
    <image src="/images/bluetooth.svg" class="bluetooth-image" />
  </view>
</view>
<scroll-view class="scrollarea" scroll-y type="list">

  <!-- 主要内容区域 -->
  <view class="main-content">

    <!-- 报警状态条 -->
    <view class="alert-banner">
      <view class="alert-icon">
        <image src="/images/alert.svg" mode="aspectFit" />
      </view>
      <text class="alert-text">{{deviceInfo.alertStatus}}</text>
    </view>

    <!-- 电池序列号信息 -->
    <view class="" style="display: flex; flex-direction: column; align-items: center;">

      <view class="serial-info">
        <text class="serial-label">电池序列号</text>
        <text class="serial-number">344656456242</text>
        <text class="more-link" bindtap="onShowMoreInfo">详情</text>
      </view>
    </view>

    <!-- 设备状态卡片 -->
    <view class="device-card">
      <!-- 电池状态可视化 -->
      <view class="battery-container">
        <view class="battery-visual battery-visual-{{deviceInfo.batteryStatus}}" bindtap="toggleBatteryStatus">
          <view class="battery-bars battery-bars-{{deviceInfo.batteryStatus}}">
            <view class="battery-bar battery-bar-{{deviceInfo.batteryStatus}}"
              wx:for="{{deviceInfo.batteryStatus === 'low' ? 7 : 50}}" wx:key="index"></view>
          </view>
          <view class="battery-status">
            <text
              class="battery-percent battery-percent-{{deviceInfo.batteryStatus}}">{{deviceInfo.batteryLevel}}%</text>
            <view class="status-badge status-badge-{{deviceInfo.batteryStatus}}">
              <text class="status-text status-text-{{deviceInfo.batteryStatus}}">{{deviceInfo.status}}</text>
            </view>
          </view>
        </view>
        <view class="battery-indicator battery-indicator-{{deviceInfo.batteryStatus}}"></view>
      </view>

      <!-- 设备参数 -->
      <view class="device-params">
        <view class="param-item">
          <text class="param-value">{{deviceInfo.voltage}}</text>
          <text class="param-label">电压</text>
        </view>
        <view class="param-item">
          <text class="param-value">{{deviceInfo.current}}</text>
          <text class="param-label">电流</text>
        </view>
        <view class="param-item">
          <text class="param-value">{{deviceInfo.power}}</text>
          <text class="param-label">功率</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-buttons">
      <view class="action-button" bindtap="onForceStart">
        <view class="button-icon">
          <image src="/images/start.svg" mode="aspectFit" tint-color="white" />
        </view>
        <text class="button-text">强制启动</text>
      </view>

      <view class="action-button" bindtap="onScheduleHeat">
        <view class="button-icon">
          <image src="/images/heating.svg" mode="aspectFit" />
        </view>
        <text class="button-text">预约加热</text>
      </view>

      <view class="action-button" bindtap="onRemoteStart">
        <view class="button-icon">
          <image src="/images/open.svg" mode="aspectFit" />
        </view>
        <text class="button-text">远程开机</text>
      </view>

      <view class="action-button" bindtap="onRemoteShutdown">
        <view class="button-icon">
          <image src="/images/shutdown.svg" mode="aspectFit" />
        </view>
        <text class="button-text">远程关机</text>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <!-- <view class="bottom-actions">
      <view class="bottom-action-button" bindtap="onUnbindDevice">
        <view class="bottom-button-icon">
          <image src="/images/unbind.svg" mode="aspectFit" />
        </view>
        <text class="bottom-button-text">解绑电池</text>
      </view>
      <view class="bottom-action-line" />
      <view class="bottom-action-button" bindtap="onScan">
        <view class="bottom-button-icon">
          <image src="/images/add.svg" mode="aspectFit" />
        </view>
        <text class="bottom-button-text">添加设备</text>
      </view>
    </view> -->
  </view>

  <!-- 地图区域 -->
  <view class="map-container">
    <map class="device-map" longitude="{{longitude}}" latitude="{{latitude}}" scale="16" show-location="{{true}}"
      markers="{{markers}}" bindtap="onMapTap">
      <view class="map-overlay">
        <view class="location-icon">
          <image src="/images/full.svg" mode="aspectFit"></image>
        </view>
      </view>
    </map>
  </view>
</scroll-view>

<!-- 扫码设备确认弹窗 -->
<t-popup visible="{{showScanConfirmPopup}}" placement="bottom" bind:visible-change="onScanPopupVisibleChange">
  <view class="scan-confirm-popup">
    <!-- 弹窗标题 -->
    <view class="popup-header">
      <text class="popup-title">请确认该设备</text>
    </view>

    <!-- 设备信息 -->
    <view class="device-info-section">
      <view class="device-info-item">
        <text class="device-info-label">电池序列号</text>
        <view class="device-info-divider"></view>
        <text class="device-info-value">{{scannedDeviceInfo.serialNumber}}</text>
      </view>

      <view class="device-info-item">
        <text class="device-info-label">固件版本</text>
        <view class="device-info-divider"></view>
        <text class="device-info-value">{{scannedDeviceInfo.firmwareVersion}}</text>
      </view>

      <view class="device-info-item">
        <text class="device-info-label">硬件版本</text>
        <view class="device-info-divider"></view>
        <text class="device-info-value">{{scannedDeviceInfo.hardwareVersion}}</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="popup-buttons">
      <t-button variant="outline" theme="primary" shape="round" size="large" class="popup-btn cancel-btn"
        bind:tap="onCancelDevice">
        取消
      </t-button>
      <t-button variant="base" theme="primary" shape="round" size="large" class="popup-btn confirm-btn"
        bind:tap="onConfirmDevice">
        确认
      </t-button>
    </view>
  </view>
</t-popup>