/**detail.ts**/
import { onPageShow } from '../../../utils/tabbar.js';
import { getDeviceBySn } from '../../../api/device';
import type { Device } from '../../../api/types';
import { mqttManager } from '../../../services/mqttManager';
import type {
  DevicePropertyData,
  DeviceEventData,
} from '../../../services/mqtt';

interface BatteryInfo {
  level: number;
  status: string;
  serialNumber: string;
}

interface VoltageCell {
  number: string;
  voltage: string;
}

interface TemperatureCell {
  number: string;
  temperature: string;
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    showBatteryInfo: false, // 控制电池信息弹窗显示
    deviceInfo: {
      id: 'device001',
      name: '智能终端设备001',
      status: '在线',
      lastUpdate: '2024-01-15 14:30:25',
    },
    // MQTT连接状态
    mqttStatus: {
      connected: false,
      connecting: false,
      error: null as string | null,
      lastUpdate: null as string | null,
    },
    records: [
      { time: '14:30', desc: '设备连接成功' },
      { time: '14:25', desc: '开始连接设备' },
      { time: '14:20', desc: '设备离线' },
      { time: '14:15', desc: '设备重启' },
    ],
    batteryInfo: {
      level: 62,
      status: '充电中',
      serialNumber: '052124004418',
    } as BatteryInfo,

    // 电压数据 - 按行组织（每行2个）
    voltageRows: [
      [
        { number: '1#', voltage: '3518' },
        { number: '5#', voltage: '3518' },
      ],
      [
        { number: '2#', voltage: '3518' },
        { number: '6#', voltage: '3518' },
      ],
      [
        { number: '3#', voltage: '3518' },
        { number: '7#', voltage: '3518' },
      ],
      [
        { number: '4#', voltage: '3518' },
        { number: '8#', voltage: '3518' },
      ],
    ] as VoltageCell[][],

    // 温度数据 - 按行组织，严格匹配设计图
    temperatureRows: [
      [
        { number: '1#', temperature: '24.1' },
        { number: '接线柱-1', temperature: '40' },
      ],
      [
        { number: '2#', temperature: '24.1' },
        { number: '接线柱-2', temperature: '40' },
      ],
      [
        { number: '3#', temperature: '24.1' },
        { number: '环境温度', temperature: '40' },
      ],
      [{ number: '4#', temperature: '24.1' }],
    ] as TemperatureCell[][],

    // 地图相关数据
    longitude: 116.397128,
    latitude: 39.916527,
    markers: [
      {
        id: 1,
        latitude: 39.916527,
        longitude: 116.397128,
        title: '设备位置',
        // iconPath: '/images/location-marker.png',
        width: 30,
        height: 30,
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    console.log('详情页面加载');
    // 页面加载初始化
    this.initPageData();
    this.getCurrentLocation();
    // 初始化MQTT服务
    this.initMqttService();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('详情页面显示');
    onPageShow(this);

    // 每次显示时刷新数据
    this.refreshData();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理MQTT监听器
    const deviceId = this.data.deviceInfo.id;
    if (deviceId) {
      mqttManager.removeDeviceListeners(deviceId);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '设备详情 - 天储BMS',
      path: '/pages/tabbar/detail/detail',
      imageUrl: '/images/share-detail.png',
    };
  },

  /**
   * 导航栏返回按钮点击事件
   */
  onGoBack() {
    wx.navigateBack();
  },

  /**
   * 显示设备列表
   */
  onShowDeviceList() {
    wx.navigateTo({
      url: '/pages/device-list/device-list',
    });
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 从缓存获取当前选中的设备
    const selectedDevice = wx.getStorageSync('selectedDevice');
    if (selectedDevice) {
      this.setData({
        deviceInfo: {
          id: selectedDevice.id || selectedDevice.sn,
          name: selectedDevice.name || selectedDevice.sn,
          status: selectedDevice.online ? '在线' : '离线',
          lastUpdate: this.formatTime(selectedDevice.uploadTime),
        },
      });
      this.fetchDeviceDetail(selectedDevice.sn);
    } else {
      // 使用默认设备ID
      const deviceId = wx.getStorageSync('selectedDeviceId');
      if (deviceId) {
        this.fetchDeviceDetail(deviceId);
      }
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp: number): string {
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  },

  /**
   * 获取设备详情
   */
  async fetchDeviceDetail(deviceSn: string) {
    try {
      wx.showLoading({
        title: '加载中...',
      });

      // 调用真实的设备详情API
      const result = await getDeviceBySn(deviceSn);

      if (result.code === 200 && result.data) {
        const device = result.data;

        // 更新设备基本信息
        this.setData({
          deviceInfo: {
            id: device.id || device.sn,
            name: device.name || device.sn,
            status: device.online ? '在线' : '离线',
            lastUpdate: this.formatTime(device.uploadTime),
          },
        });

        // 解析设备属性数据
        this.parseDeviceProperties(device.properties);
      } else {
        // API调用失败，使用模拟数据
        console.warn('API调用失败，使用模拟数据');
        const deviceDetail = await this.getDeviceDetailFromAPI(deviceSn);

        this.setData({
          batteryInfo: deviceDetail.batteryInfo,
          voltageRows: deviceDetail.voltageRows,
          temperatureRows: deviceDetail.temperatureRows,
        });
      }
    } catch (error) {
      console.error('获取设备详情失败:', error);

      // 错误时使用模拟数据
      const deviceDetail = await this.getDeviceDetailFromAPI(deviceSn);

      this.setData({
        batteryInfo: deviceDetail.batteryInfo,
        voltageRows: deviceDetail.voltageRows,
        temperatureRows: deviceDetail.temperatureRows,
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 解析设备属性数据
   */
  parseDeviceProperties(properties: Record<string, any>) {
    // 根据实际的设备属性结构解析数据
    // 这里需要根据你的后端返回的数据结构进行调整
    try {
      const batteryInfo = {
        level: properties.batteryLevel || 62,
        status: properties.batteryStatus || '充电中',
        serialNumber: properties.serialNumber || '052124004418',
      };

      // 解析电压数据
      const voltageData = properties.voltages || [];
      const voltageRows = [];
      for (let i = 0; i < voltageData.length; i += 2) {
        const row = [];
        if (voltageData[i]) {
          row.push({
            number: `${i + 1}#`,
            voltage: voltageData[i].toString(),
          });
        }
        if (voltageData[i + 1]) {
          row.push({
            number: `${i + 2}#`,
            voltage: voltageData[i + 1].toString(),
          });
        }
        if (row.length > 0) {
          voltageRows.push(row);
        }
      }

      // 解析温度数据
      const temperatureData = properties.temperatures || [];
      const temperatureRows = [];
      for (let i = 0; i < temperatureData.length; i += 2) {
        const row = [];
        if (temperatureData[i]) {
          row.push({
            number: `${i + 1}#`,
            temperature: temperatureData[i].toString(),
          });
        }
        if (temperatureData[i + 1]) {
          row.push({
            number: `接线柱-${Math.floor(i / 2) + 1}`,
            temperature: temperatureData[i + 1].toString(),
          });
        }
        if (row.length > 0) {
          temperatureRows.push(row);
        }
      }

      this.setData({
        batteryInfo,
        voltageRows:
          voltageRows.length > 0 ? voltageRows : this.data.voltageRows,
        temperatureRows:
          temperatureRows.length > 0
            ? temperatureRows
            : this.data.temperatureRows,
      });
    } catch (error) {
      console.error('解析设备属性失败:', error);
      // 解析失败时保持原有数据
    }
  },

  /**
   * 模拟API调用
   */
  async getDeviceDetailFromAPI(deviceId: string): Promise<any> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          batteryInfo: {
            level: Math.floor(Math.random() * 40) + 60, // 60-100%
            status: Math.random() > 0.5 ? '充电中' : '待机中',
            serialNumber: deviceId || '052124004418',
          },
          voltageRows: [
            [
              {
                number: '1#',
                voltage: (3500 + Math.floor(Math.random() * 50)).toString(),
              },
              {
                number: '5#',
                voltage: (3500 + Math.floor(Math.random() * 50)).toString(),
              },
            ],
            [
              {
                number: '2#',
                voltage: (3500 + Math.floor(Math.random() * 50)).toString(),
              },
              {
                number: '6#',
                voltage: (3500 + Math.floor(Math.random() * 50)).toString(),
              },
            ],
            [
              {
                number: '3#',
                voltage: (3500 + Math.floor(Math.random() * 50)).toString(),
              },
              {
                number: '7#',
                voltage: (3500 + Math.floor(Math.random() * 50)).toString(),
              },
            ],
            [
              {
                number: '4#',
                voltage: (3500 + Math.floor(Math.random() * 50)).toString(),
              },
              {
                number: '8#',
                voltage: (3500 + Math.floor(Math.random() * 50)).toString(),
              },
            ],
          ],
          temperatureRows: [
            [
              {
                number: '1#',
                temperature: (20 + Math.random() * 10).toFixed(1),
              },
              {
                number: '接线柱-1',
                temperature: (35 + Math.random() * 10).toFixed(0),
              },
            ],
            [
              {
                number: '2#',
                temperature: (20 + Math.random() * 10).toFixed(1),
              },
              {
                number: '接线柱-2',
                temperature: (35 + Math.random() * 10).toFixed(0),
              },
            ],
            [
              {
                number: '3#',
                temperature: (20 + Math.random() * 10).toFixed(1),
              },
              {
                number: '环境温度',
                temperature: (35 + Math.random() * 10).toFixed(0),
              },
            ],
            [
              {
                number: '4#',
                temperature: (20 + Math.random() * 10).toFixed(1),
              },
            ],
          ],
        });
      }, 1000);
    });
  },

  /**
   * 刷新数据
   */
  refreshData() {
    const selectedDevice = wx.getStorageSync('selectedDevice');
    if (selectedDevice) {
      this.setData({
        deviceInfo: {
          id: selectedDevice.id || selectedDevice.sn,
          name: selectedDevice.name || selectedDevice.sn,
          status: selectedDevice.online ? '在线' : '离线',
          lastUpdate: this.formatTime(selectedDevice.uploadTime),
        },
      });
      this.fetchDeviceDetail(selectedDevice.sn);
    } else {
      const deviceId = wx.getStorageSync('selectedDeviceId') || '';
      if (!deviceId) return;
      this.fetchDeviceDetail(deviceId);
    }
  },

  /**
   * 显示更多信息
   */
  onShowMoreInfo() {
    this.setData({
      showBatteryInfo: true,
    });
  },

  /**
   * 关闭电池信息弹窗
   */
  onCloseBatteryInfo() {
    this.setData({
      showBatteryInfo: false,
    });
  },

  /**
   * 弹窗显示状态变化
   */
  onBatteryInfoVisibleChange(event: any) {
    this.setData({
      showBatteryInfo: event.detail.visible,
    });
  },

  /**
   * 导出数据
   */
  onExportData() {
    wx.showActionSheet({
      itemList: ['导出电压数据', '导出温度数据', '导出全部数据'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.exportVoltageData();
            break;
          case 1:
            this.exportTemperatureData();
            break;
          case 2:
            this.exportAllData();
            break;
        }
      },
    });
  },

  /**
   * 导出电压数据
   */
  exportVoltageData() {
    const voltageData = this.data.voltageRows.flat();
    const csvContent = voltageData
      .map((item: VoltageCell) => `${item.number},${item.voltage}mV`)
      .join('\n');
    console.log('电压数据:', csvContent);

    wx.showToast({
      title: '电压数据已导出',
      icon: 'success',
    });
  },

  /**
   * 导出温度数据
   */
  exportTemperatureData() {
    const temperatureData = this.data.temperatureRows.flat();
    const csvContent = temperatureData
      .map((item: TemperatureCell) => `${item.number},${item.temperature}℃`)
      .join('\n');
    console.log('温度数据:', csvContent);

    wx.showToast({
      title: '温度数据已导出',
      icon: 'success',
    });
  },

  /**
   * 导出全部数据
   */
  exportAllData() {
    const allData = {
      batteryInfo: this.data.batteryInfo,
      voltageData: this.data.voltageRows.flat(),
      temperatureData: this.data.temperatureRows.flat(),
      exportTime: new Date().toISOString(),
    };

    console.log('全部数据:', JSON.stringify(allData, null, 2));

    wx.showToast({
      title: '全部数据已导出',
      icon: 'success',
    });
  },

  // 解绑设备
  onUnbindDevice() {
    wx.showModal({
      title: '确认解绑',
      content: '确定要解绑当前设备吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '解绑成功',
            icon: 'success',
          });
          // TODO: 实现解绑逻辑
        }
      },
    });
  },

  onRecord() {
    const { deviceInfo } = this.data;
    wx.navigateTo({
      url: '/pages/charging-record/charging-record?deviceId=' + deviceInfo.id,
    });
  },

  // 地图点击事件
  onMapTap() {
    wx.openLocation({
      latitude: this.data.latitude,
      longitude: this.data.longitude,
      name: '设备位置',
      address: '设备详细地址',
    });
  },

  // 获取当前位置
  getCurrentLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('获取位置成功:', res);
        this.setData({
          longitude: res.longitude,
          latitude: res.latitude,
          'markers[0].longitude': res.longitude,
          'markers[0].latitude': res.latitude,
        });
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        wx.showToast({
          title: '获取位置失败',
          icon: 'none',
        });
      },
    });
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '设备详情 - 天储BMS',
      imageUrl: '/images/share-detail.png',
    };
  },

  /**
   * 初始化MQTT服务
   */
  async initMqttService() {
    try {
      this.setData({
        'mqttStatus.connecting': true,
        'mqttStatus.error': null,
      });

      console.log('开始初始化MQTT服务...');
      await mqttManager.initialize();

      this.setData({
        'mqttStatus.connected': true,
        'mqttStatus.connecting': false,
        'mqttStatus.lastUpdate': new Date().toLocaleString(),
      });

      // 设置设备数据监听
      this.setupDeviceDataListeners();

      console.log('MQTT服务初始化成功');
    } catch (error) {
      console.error('MQTT服务初始化失败:', error);
      this.setData({
        'mqttStatus.connected': false,
        'mqttStatus.connecting': false,
        'mqttStatus.error': '连接失败',
      });

      wx.showToast({
        title: 'MQTT连接失败',
        icon: 'none',
        duration: 2000,
      });
    }
  },

  /**
   * 设置设备数据监听器
   */
  setupDeviceDataListeners() {
    const deviceId = this.data.deviceInfo.id;
    if (!deviceId) return;

    // 监听设备属性更新
    mqttManager.onDevicePropertyUpdate(deviceId, (data: DevicePropertyData) => {
      console.log('收到设备属性更新:', data);
      this.handleDevicePropertyUpdate(data);
    });

    // 监听设备事件
    mqttManager.onDeviceEvent(deviceId, (data: DeviceEventData) => {
      console.log('收到设备事件:', data);
      this.handleDeviceEvent(data);
    });

    console.log(`已设置设备 ${deviceId} 的数据监听器`);
  },

  /**
   * 处理设备属性更新
   */
  handleDevicePropertyUpdate(data: DevicePropertyData) {
    const { properties } = data;

    // 更新电池信息
    if (properties.batteryLevel !== undefined) {
      this.setData({
        'batteryInfo.level': properties.batteryLevel,
      });
    }

    if (properties.batteryStatus !== undefined) {
      this.setData({
        'batteryInfo.status': properties.batteryStatus,
      });
    }

    // 更新电压数据
    if (properties.voltages && Array.isArray(properties.voltages)) {
      const voltageRows = this.formatVoltageData(properties.voltages);
      this.setData({
        voltageRows: voltageRows,
      });
    }

    // 更新温度数据
    if (properties.temperatures && Array.isArray(properties.temperatures)) {
      const temperatureRows = this.formatTemperatureData(
        properties.temperatures
      );
      this.setData({
        temperatureRows: temperatureRows,
      });
    }

    // 更新设备状态
    this.setData({
      'deviceInfo.status': '在线',
      'deviceInfo.lastUpdate': new Date().toLocaleString(),
      'mqttStatus.lastUpdate': new Date().toLocaleString(),
    });

    console.log('设备属性已更新');
  },

  /**
   * 处理设备事件
   */
  handleDeviceEvent(data: DeviceEventData) {
    const { eventType, eventData } = data;

    // 根据事件类型处理不同的事件
    switch (eventType) {
      case 'alarm':
        this.handleAlarmEvent(eventData);
        break;
      case 'status_change':
        this.handleStatusChangeEvent(eventData);
        break;
      default:
        console.log('未处理的设备事件:', eventType, eventData);
    }

    // 更新最后更新时间
    this.setData({
      'mqttStatus.lastUpdate': new Date().toLocaleString(),
    });
  },

  /**
   * 处理报警事件
   */
  handleAlarmEvent(eventData: Record<string, any>) {
    console.log('收到报警事件:', eventData);

    // 显示报警提示
    wx.showModal({
      title: '设备报警',
      content: eventData.message || '设备发生异常，请及时处理',
      showCancel: false,
    });
  },

  /**
   * 处理状态变化事件
   */
  handleStatusChangeEvent(eventData: Record<string, any>) {
    console.log('设备状态变化:', eventData);

    if (eventData.status) {
      this.setData({
        'deviceInfo.status': eventData.status,
      });
    }
  },

  /**
   * 格式化电压数据
   */
  formatVoltageData(voltages: number[]): any[][] {
    const rows = [];
    for (let i = 0; i < voltages.length; i += 2) {
      const row = [];
      if (voltages[i] !== undefined) {
        row.push({
          number: `${i + 1}#`,
          voltage: voltages[i].toString(),
        });
      }
      if (voltages[i + 1] !== undefined) {
        row.push({
          number: `${i + 2}#`,
          voltage: voltages[i + 1].toString(),
        });
      }
      if (row.length > 0) {
        rows.push(row);
      }
    }
    return rows;
  },

  /**
   * 格式化温度数据
   */
  formatTemperatureData(temperatures: number[]): any[][] {
    const rows = [];
    for (let i = 0; i < temperatures.length; i += 2) {
      const row = [];
      if (temperatures[i] !== undefined) {
        row.push({
          number: `${i + 1}#`,
          temperature: temperatures[i].toString(),
        });
      }
      if (temperatures[i + 1] !== undefined) {
        row.push({
          number: `${i + 2}#`,
          temperature: temperatures[i + 1].toString(),
        });
      }
      if (row.length > 0) {
        rows.push(row);
      }
    }
    return rows;
  },
});
