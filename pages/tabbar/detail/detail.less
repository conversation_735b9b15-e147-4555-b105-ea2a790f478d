/* pages/detail/detail.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.page-container {
  flex: 1;
}

/* 主要内容区域 */
.main-content {
  // padding: 20rpx 16rpx;
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom)); /* 适配tabbar */
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.bottom-main-content {
  margin: 0 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 电池状态卡片 */
.battery-status-card {
  background: #ffffff;
  border-bottom-left-radius: 60rpx;
  border-bottom-right-radius: 60rpx;
  overflow: hidden;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0px 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 电池容器 */
.battery-container {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

/* 电池可视化 */
.battery-visual {
  position: relative;
  width: 456rpx;
  height: 240rpx;
  background: #e3f9e9;
  border-radius: 19rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 充电中的电池条 */
.battery-bars {
  position: absolute;
  left: 0;
  top: 0;
  width: 62%; /* 根据电量百分比调整 */
  height: 100%;
  background: linear-gradient(180deg, #1ac74b 0%, #24e86f 100%);
  box-shadow: 0rpx 0rpx 53rpx 2rpx rgba(7, 217, 67, 0.3),
    0rpx 0rpx 11rpx 0rpx rgba(7, 217, 67, 0.3),
    inset 0rpx 0rpx 16rpx 10rpx rgba(0, 181, 51, 0.2);
  border-radius: 19rpx 0 0 19rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.charging-indicator {
  width: 100%;
  height: 100%;
  background: transparent;
}

.battery-status {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  z-index: 10;
}

.battery-percent {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 48rpx;
  line-height: 1;
  color: #333333;
}

.status-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 4rpx 12rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.charging-icon {
  width: 28rpx;
  height: 28rpx;
}

.status-text {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 24rpx;
  line-height: 1;
  color: #333333;
}

/* 电池指示器 - 右侧绿色条 */
.battery-indicator.charging {
  width: 11rpx;
  height: 73rpx;
  background: #07d340;
  border-radius: 0rpx 8rpx 8rpx 0rpx;
}

/* 序列号信息 */
.serial-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 8rpx 16rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  margin-top: 20rpx;
}

.serial-label {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 26rpx;
  line-height: 1;
  color: #666666;
}

.serial-number {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 26rpx;
  line-height: 1;
  color: #333333;
}

.more-link {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 26rpx;
  line-height: 1;
  color: var(--primaryColor);
  text-decoration: none;
}

/* 信息卡片通用样式 */
.voltage-info-card,
.temperature-info-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  box-shadow: 0px 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 卡片标题 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24rpx;
  margin-bottom: 8rpx;
}

.title-line.dashed {
  flex: 1;
  height: 1rpx;
  border-bottom: 1rpx dashed #e0e0e0;
}

.title-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 0 16rpx;
}

.title-icon {
  width: 40rpx;
  height: 40rpx;
}

.title-text {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 28rpx;
  line-height: 1;
  color: #333333;
}

/* 电压网格 */
.voltage-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.voltage-row {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 16rpx;
}

/* 电压单元样式 */
.voltage-cell {
  display: flex;
  align-items: center;
  gap: 0;
  flex: 1;
  background: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;
}

.voltage-cell .cell-header {
  background: #e8f5e8;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
  min-width: 24rpx;
  padding: 0 12rpx;
  border-radius: 8rpx;
}

.voltage-cell .cell-number {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 26rpx;
  line-height: 1;
  color: #00c851;
}

.cell-divider {
  flex: 1;
  margin-left: 16rpx;
  border-bottom: 1rpx dashed #e7e7e7;
}

.cell-value {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  padding: 16rpx 20rpx;
  justify-content: center;
}

.value-number {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 28rpx;
  line-height: 1;
  color: #333333;
}

.value-unit {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 22rpx;
  line-height: 1;
  color: #999999;
}

/* 温度网格 */
.temperature-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.temperature-row {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 16rpx;
}

/* 最后一行只有一个元素时的处理 */
.temperature-row:last-child {
  justify-content: flex-start;
}

.temperature-row:last-child .temperature-cell {
  flex: 0 0 48%;
}

/* 温度单元样式 */
.temperature-cell {
  display: flex;
  align-items: center;
  gap: 0;
  flex: 1;
  background: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;
}

/* 温度卡片的特殊样式 */
.temperature-cell .cell-header.temperature {
  background: #fff4e6;
  // padding: 16rpx 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
  min-width: 24rpx;
  padding: 0 12rpx;
  border-radius: 8rpx;
}

.temperature-cell .cell-number {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 26rpx;
  line-height: 1;
  color: #ff9500;
  white-space: nowrap;
}

/* 特殊的温度传感器标签 */
// .temperature-cell:nth-child(2) .cell-header.temperature,
// .temperature-cell:nth-child(4) .cell-header.temperature,
// .temperature-cell:nth-child(6) .cell-header.temperature {
//   padding: 16rpx 8rpx;
//   min-width: 80rpx;
// }

// .temperature-cell:nth-child(2) .cell-number,
// .temperature-cell:nth-child(4) .cell-number,
// .temperature-cell:nth-child(6) .cell-number {
//   font-size: 22rpx;
//   white-space: nowrap;
// }

/* 充电动画效果 */
.battery-bars {
  animation: charging-pulse 2s ease-in-out infinite;
}

@keyframes charging-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 电池信息弹窗样式 */
.battery-info-popup {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.popup-header {
  padding: 40rpx 32rpx 32rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 36rpx;
  line-height: 1;
  color: #333333;
}

.popup-content {
  padding: 32rpx;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  gap: 16rpx;
}

.info-label {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 32rpx;
  line-height: 1;
  color: #666666;
  flex-shrink: 0;
}

.info-divider {
  flex: 1;
  height: 1rpx;
  border-bottom: 1rpx dashed #e0e0e0;
  margin: 0 16rpx;
}

.info-value {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 32rpx;
  line-height: 1;
  color: #333333;
  flex-shrink: 0;
}

.popup-footer {
  padding: 0 32rpx 10rpx;
  text-align: center;
}

/* 自定义返回按钮样式 */
.return-btn {
  color: var(--primaryColor) !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  padding: 16rpx 0 !important;
  background: transparent !important;
  border: none !important;
}

.return-btn:after {
  border: none !important;
}

/* 交互反馈 */
.more-link:active {
  color: #0056cc;
  transform: scale(0.95);
  transition: all 0.1s ease;
}

.voltage-cell:active,
.temperature-cell:active {
  transform: scale(0.98);
  transition: all 0.1s ease;
}

/* 安全区域适配 */
// @supports (padding: max(0px)) {
//   .main-content {
//     padding-left: max(16rpx, env(safe-area-inset-left));
//     padding-right: max(16rpx, env(safe-area-inset-right));
//   }

//   .battery-status-card {
//     margin-left: max(-16rpx, -env(safe-area-inset-left));
//     margin-right: max(-16rpx, -env(safe-area-inset-right));
//   }
// }

/* 底部操作按钮区域 */
.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 60rpx;
  margin: 20rpx 20rpx 0 20rpx;
}

.bottom-action-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
  padding: 12rpx;
  background: #ffffff;
  flex: 1;
  transition: all 0.2s ease;
}

.bottom-action-button:active {
  opacity: 0.8;
}

.bottom-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44rpx;
  height: 44rpx;
}

.bottom-button-icon image {
  width: 40rpx;
  height: 40rpx;
  // filter: invert(34%) sepia(64%) saturate(4645%) hue-rotate(214deg)
  //   brightness(101%) contrast(102%);
}

.bottom-button-text {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 28rpx;
  line-height: 36rpx;
  color: #333333;
  text-align: left;
}
.bottom-action-line {
  width: 1rpx;
  height: 44rpx;
  background: #e7e7e7;
}

/* 地图区域 */
.map-container {
  position: relative;
  height: 484rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.device-map {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4rpx;
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(12rpx);
  border-radius: 16rpx;
}

.location-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 54rpx;
  height: 54rpx;
}

/* 操作按钮区域 */
.action-buttons {
  width: 100%;
  position: fixed;
  bottom: 0;
  box-sizing: border-box;
  z-index: 99;

  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 30rpx 30rpx 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background: #ffffff;
}

.action-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4rpx;
  padding: 16rpx 28rpx;
  background: #f2f3ff;
  border-radius: 16rpx;
  flex: 1;
  min-height: 88rpx;
}

.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
}

.button-text {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 24rpx;
  line-height: 44rpx;
  color: var(--primaryColor);
  text-align: center;
}

/* 动画效果 */
.action-button {
  transition: all 0.2s ease;
}

.action-button:active {
  transform: scale(0.95);
  background: #e8e9ff;
}
