/* scan-device-info.less */
page {
  height: 100vh;
  background: #ffffff;
}

/* 滚动区域 */
.scroll-view {
  width: 100%;
  height: calc(100vh - 128rpx);
  padding-bottom: 40rpx;
}

/* 设备信息卡片 */
.device-info-card {
  margin: 0;
  padding: 40rpx 48rpx;
  background: #ffffff;
  box-shadow: 0px 16rpx 20rpx -10rpx rgba(0, 0, 0, 0.08),
    0px 32rpx 48rpx 4rpx rgba(0, 0, 0, 0.04),
    0px 12rpx 60rpx 10rpx rgba(0, 0, 0, 0.05);
  border-radius: 0rpx 0rpx 40rpx 40rpx;
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.card-title {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 36rpx;
  line-height: 52rpx;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

/* 状态标签 */
.status-tag {
  display: flex;
  align-items: center;
  padding: 8rpx 28rpx;
  border-radius: 100rpx;
  background: #f5f5f5;
}

.status-tag.bound {
  background: linear-gradient(135deg, #f2f3ff 0%, #ffffff 100%);
}

.status-tag.unbound {
  background: #f5f5f5;
}

.status-text {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 28rpx;
  line-height: 36rpx;
  color: rgba(0, 0, 0, 0.6);
  text-align: center;
}

/* 设备基本信息 */
.device-basic-info {
  margin-bottom: 32rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx 0;
}

.info-label {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 28rpx;
  line-height: 36rpx;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
}

.info-divider {
  flex: 1;
  height: 0;
  border-top: 2rpx dashed #e7e7e7;
}

.info-value {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 28rpx;
  line-height: 32rpx;
  color: rgba(0, 0, 0, 0.9);
  white-space: nowrap;
}



/* 当前用户绑定信息 */
.current-user-section {
  margin-top: 32rpx;
}

.user-info-card {
  background: linear-gradient(135deg, #f2f3ff 0%, #ffffff 100%);
  border-radius: 24rpx;
  padding: 32rpx 0;
  margin-bottom: 20rpx;
}

.user-info-card.current-user {
  background: linear-gradient(135deg, #f2f3ff 0%, #ffffff 100%);
}

.user-info-card.other-user {
  background: #f2f3ff;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding: 0 32rpx;
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  overflow: hidden;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #d0d0d0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.avatar-text {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 48rpx;
  color: #ffffff;
}

.user-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-name {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 32rpx;
  line-height: 48rpx;
  color: rgba(0, 0, 0, 0.9);
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.contact-tag {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 16rpx;
  background: #f3f3f3;
  border-radius: 12rpx;
}

.contact-icon {
  width: 32rpx;
  height: 32rpx;
}

.contact-number {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 24rpx;
  line-height: 32rpx;
  color: rgba(0, 0, 0, 0.9);
}

/* 车辆信息 */
.vehicle-info {
  padding: 0 32rpx;
}

.vehicle-info .info-item {
  // padding: 24rpx;
}

/* 其他绑定人分组 */
.other-users-section {
  margin-top: 40rpx;
  padding: 0 24rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}

.section-title {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 36rpx;
  line-height: 52rpx;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

.other-users-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* 底部占位 */
.bottom-placeholder {
  height: 180rpx;
}

/* 底部按钮区域 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background: #ffffff;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.button-row {
  display: flex;
  gap: 32rpx;
}

.action-btn {
  flex: 1;
}

.bind-btn {
  flex: 1;
  --td-button-primary-bg-color: #0052d9;
  --td-button-primary-border-color: #0052d9;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .card-title,
  .section-title {
    font-size: 32rpx;
  }

  .info-label,
  .info-value {
    font-size: 26rpx;
  }

  .user-name {
    font-size: 30rpx;
  }

  .contact-number {
    font-size: 22rpx;
  }
}
