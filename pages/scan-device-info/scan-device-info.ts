// scan-device-info.ts
import { getDeviceBySn } from '../../api/device';
import { bindDevice } from '../../api/user';
import type { Device, BindDeviceRequest, BaseResponse } from '../../api/types';
const app = getApp<IAppOption>();

// 绑定用户信息类型
interface BoundUserInfo {
  username: string;
  mobile: string;
  avatar?: string;
  carModel?: string;
  plateNumber?: string;
  isCurrentUser: boolean;
}

Page({
  data: {
    safeAreaBottom: 0,

    // 设备信息
    deviceInfo: {
      serialNumber: '',
      firmwareVersion: '',
      hardwareVersion: '',
      name: '',
      online: false,
    },

    // 绑定状态
    deviceBound: false, // 当前用户是否已绑定
    hasOtherBoundUsers: false, // 是否有其他绑定用户

    // 当前用户绑定信息
    currentUserInfo: null as BoundUserInfo | null,

    // 其他绑定人信息
    otherBoundUsers: [] as BoundUserInfo[],

    // 加载状态
    loading: false,
  },

  onLoad(options: any) {
    // 获取系统信息
    this.getSystemInfo();
    // 如果有扫码参数，可以在这里处理
    if (options.deviceId) {
      this.loadDeviceInfo(options.deviceId);
    }
  },

  // 获取系统信息
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      safeAreaBottom: systemInfo.safeArea
        ? systemInfo.windowHeight - systemInfo.safeArea.bottom
        : 0,
    });
  },

  // 加载设备信息
  async loadDeviceInfo(deviceId: string) {
    console.log('加载设备信息:', deviceId);

    this.setData({ loading: true });

    try {
      wx.showLoading({
        title: '加载设备信息...',
      });

      const response: BaseResponse<Device> = await getDeviceBySn(deviceId);
      console.log('获取设备信息:', response);
      
      if (response) {
        const device: Device = response;
        const firmwareVersion = device?.properties?.FirmwareVersion
          ? `V ${device.properties.FirmwareVersion}`
          : '未知';
        const hardwareVersion = device?.properties?.HardwareVersion
          ? `V ${device.properties.HardwareVersion}`
          : '未知';

        // 处理绑定用户信息
        const boundUsers = device.edges?.wxmp_user || [];
        const currentUserOpenid = app.globalData.userInfo?.wx_openid;
        
        let currentUserInfo: BoundUserInfo | null = null;
        const otherBoundUsers: BoundUserInfo[] = [];
        let deviceBound = false;

        boundUsers.forEach((wxmpUser) => {
          const userInfo: BoundUserInfo = {
            username: wxmpUser.nickname || '未知用户',
            mobile: wxmpUser.mobile || '未知',
            avatar: wxmpUser.avatar,
            carModel: 'V 1.04', // 这里需要根据实际数据结构调整
            plateNumber: '沪V 89311', // 这里需要根据实际数据结构调整
            isCurrentUser: wxmpUser.wx_openid === currentUserOpenid,
          };

          if (wxmpUser.wx_openid === currentUserOpenid) {
            currentUserInfo = userInfo;
            deviceBound = true;
          } else {
            otherBoundUsers.push(userInfo);
          }
        });

        this.setData({
          'deviceInfo.serialNumber': device.sn,
          'deviceInfo.firmwareVersion': firmwareVersion,
          'deviceInfo.hardwareVersion': hardwareVersion,
          'deviceInfo.name': device.name || `设备${device.sn}`,
          'deviceInfo.online': device.online,
          deviceBound,
          currentUserInfo,
          otherBoundUsers,
          hasOtherBoundUsers: otherBoundUsers.length > 0,
        });
      } else {
        throw new Error(response.message || '获取设备信息失败');
      }
    } catch (error: any) {
      console.error('加载设备信息失败:', error);
      wx.showToast({
        title: error.message || '获取设备信息失败',
        icon: 'none',
        duration: 2000,
      });

      // 设备不存在或获取失败时的默认处理
      this.setData({
        'deviceInfo.serialNumber': deviceId,
        'deviceInfo.firmwareVersion': '未知',
        'deviceInfo.hardwareVersion': '未知',
        'deviceInfo.name': `设备${deviceId}`,
        'deviceInfo.online': false,
        deviceBound: false,
        currentUserInfo: null,
        otherBoundUsers: [],
        hasOtherBoundUsers: false,
      });
    } finally {
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },
  // 返回上一页
  goBack() {
    wx.navigateBack({
      delta: 1,
    });
  },

  // 确认绑定
  async onConfirmBind() {
    const { deviceInfo } = this.data;
    wx.navigateTo({
      url: `/pages/bind-device/bind-device?deviceId=${deviceInfo.serialNumber}`,
    });
  },
});
