<!--scan-device-info.wxml-->
<t-navbar title="扫码信息" left-arrow bind:go-back="goBack" />

<!-- 滚动内容区域 -->
<scroll-view class="scroll-view" scroll-y>
  <!-- 设备信息卡片 -->
  <view class="device-info-card">
    <view class="card-header">
      <text class="card-title">设备信息</text>
      <view class="status-tag {{deviceBound ? 'bound' : 'unbound'}}">
        <text class="status-text">{{deviceBound ? '您已绑定该设备' : '您暂未绑定该设备'}}</text>
      </view>
    </view>

    <!-- 设备基本信息 -->
    <view class="device-basic-info" wx:if="{{!deviceBound}}">
      <view class="info-item">
        <text class="info-label">电池序列号</text>
        <view class="info-divider"></view>
        <text class="info-value">{{deviceInfo.serialNumber}}</text>
      </view>

      <view class="info-item">
        <text class="info-label">固件版本</text>
        <view class="info-divider"></view>
        <text class="info-value">{{deviceInfo.firmwareVersion}}</text>
      </view>

      <view class="info-item">
        <text class="info-label">硬件版本</text>
        <view class="info-divider"></view>
        <text class="info-value">{{deviceInfo.hardwareVersion}}</text>
      </view>
    </view>

    <!-- 当前用户绑定信息 -->
    <view class="current-user-section" wx:if="{{deviceBound && currentUserInfo}}">
      <view class="user-info-card current-user">
        <view class="user-info">
          <view class="user-avatar">
            <image src="{{currentUserInfo.avatar}}" class="avatar-image" wx:if="{{currentUserInfo.avatar}}" />
            <view class="avatar-placeholder" wx:else>
              <text class="avatar-text">{{currentUserInfo.username.charAt(0)}}</text>
            </view>
          </view>
          <view class="user-content">
            <text class="user-name">{{currentUserInfo.username}}</text>
            <view class="contact-info">
              <view class="contact-tag">
                <image src="/images/mobile.svg" class="contact-icon" />
              </view>
              <text class="contact-number">{{currentUserInfo.mobile}}</text>
            </view>
          </view>
        </view>
        
        <!-- 车辆信息 -->
        <view class="vehicle-info">
          <view class="info-item">
            <text class="info-label">车型</text>
            <view class="info-divider"></view>
            <text class="info-value">{{currentUserInfo.carModel}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">车牌号码</text>
            <view class="info-divider"></view>
            <text class="info-value">{{currentUserInfo.plateNumber}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">电池序列号</text>
            <view class="info-divider"></view>
            <text class="info-value">{{deviceInfo.serialNumber}}</text>
          </view>
    
          <view class="info-item">
            <text class="info-label">固件版本</text>
            <view class="info-divider"></view>
            <text class="info-value">{{deviceInfo.firmwareVersion}}</text>
          </view>
    
          <view class="info-item">
            <text class="info-label">硬件版本</text>
            <view class="info-divider"></view>
            <text class="info-value">{{deviceInfo.hardwareVersion}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 其他绑定人信息 -->
  <view class="other-users-section" wx:if="{{hasOtherBoundUsers}}">
    <view class="section-header">
      <text class="section-title">其他绑定人</text>
    </view>
    
    <view class="other-users-list">
      <view class="user-info-card other-user" wx:for="{{otherBoundUsers}}" wx:key="mobile">
        <view class="user-info">
          <view class="user-avatar">
            <image src="{{item.avatar}}" class="avatar-image" wx:if="{{item.avatar}}" />
            <view class="avatar-placeholder" wx:else>
              <text class="avatar-text">{{item.username.charAt(0)}}</text>
            </view>
          </view>
          <view class="user-content">
            <text class="user-name">{{item.username}}</text>
            <view class="contact-info">
              <view class="contact-tag">
                <image src="/images/mobile.svg" class="contact-icon" />
                <text class="contact-number">{{item.mobile}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 车辆信息 -->
        <view class="vehicle-info">
          <view class="info-item">
            <text class="info-label">车型</text>
            <view class="info-divider"></view>
            <text class="info-value">{{item.carModel}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">车牌号码</text>
            <view class="info-divider"></view>
            <text class="info-value">{{item.plateNumber}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部占位，确保按钮不被遮挡 -->
  <view class="bottom-placeholder" wx:if="{{!deviceBound}}"></view>
</scroll-view>

<!-- 底部按钮区域 - 仅在未绑定时显示 -->
<view class="bottom-buttons" wx:if="{{!deviceBound}}" style="padding-bottom: {{safeAreaBottom}}px;">
  <view class="button-row">
    <t-button variant="base" theme="primary" shape="round" size="large" class="bind-btn"
      bind:tap="onConfirmBind">
      去绑定
    </t-button>
  </view>
</view>