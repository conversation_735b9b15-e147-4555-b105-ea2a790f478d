/* pages/bluetooth-connect/bluetooth-connect.less */

page {
  height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  padding-bottom: calc(env(safe-area-inset-bottom));
}

.container {
  padding: 32rpx;
  min-height: calc(100vh - 88rpx); /* 减去navbar高度 */
  display: flex;
  flex-direction: column;
  gap: 24rpx; /* 减少间距 */
}

/* 设备信息卡片 */
.device-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 0 8rpx; /* 左右添加间距 */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.device-header {
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.device-icon {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.device-icon.connected {
  background: linear-gradient(135deg, #07d943 0%, #65f59c 100%);
  box-shadow: 0 0 32rpx rgba(7, 217, 67, 0.4);
}

.device-image {
  width: 60rpx;
  height: 60rpx;
  filter: brightness(0) invert(1);
}

.connection-status {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border: 4rpx solid #ffffff;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.connection-status.active {
  background: #2ed573;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(46, 213, 115, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(46, 213, 115, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(46, 213, 115, 0);
  }
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.device-id {
  font-size: 24rpx;
  color: #999999;
  font-family: monospace;
  margin-bottom: 8rpx;
}

.connection-text {
  font-size: 28rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  display: inline-block;
}

.connection-text.connected {
  background: rgba(46, 213, 115, 0.1);
  color: #2ed573;
}

.connection-text.disconnected {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

/* 连接进度 */
.connection-progress {
  margin-top: 32rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f1f2f6;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
}

/* 数据显示区域 */
.data-section {
  flex: 1;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 28rpx;
  margin: 0 8rpx; /* 左右添加间距 */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.data-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 8rpx 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.data-status.receiving {
  background: rgba(46, 213, 115, 0.1);
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  background: #999999;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.data-status.receiving .status-dot {
  background: #2ed573;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0.3;
  }
}

.status-text {
  font-size: 24rpx;
  color: #666666;
}

.data-status.receiving .status-text {
  color: #2ed573;
}

/* 数据卡片列表 */
.data-cards {
  max-height: 600rpx;
  overflow-y: auto;
}

.data-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-left: 6rpx solid #667eea;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.data-time {
  font-size: 24rpx;
  color: #999999;
  font-family: monospace;
}

.data-type {
  font-size: 20rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  text-transform: uppercase;
}

.data-content {
  line-height: 1.6;
}

.data-text {
  font-size: 28rpx;
  color: #333333;
  word-break: break-all;
}

.data-hex {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.hex-label {
  font-size: 24rpx;
  color: #999999;
  font-weight: 500;
}

.hex-value {
  font-size: 24rpx;
  color: #333333;
  font-family: monospace;
  word-break: break-all;
  line-height: 1.8;
}

.data-json {
  background: #2f3542;
  padding: 16rpx;
  border-radius: 12rpx;
  margin-top: 8rpx;
}

.json-data {
  font-size: 24rpx;
  color: #a4b0be;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 数据统计 */
.data-stats {
  display: flex;
  justify-content: space-around;
  padding: 32rpx 0;
  border-top: 2rpx solid #f1f2f6;
  margin-top: 32rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #667eea;
}

.stat-label {
  font-size: 24rpx;
  color: #999999;
}

/* 空数据状态 */
.empty-data {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 0 8rpx; /* 左右添加间距 */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}

.empty-image {
  width: 60rpx;
  height: 60rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999999;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
  padding: 24rpx 8rpx 0 8rpx; /* 左右添加间距 */
}

.action-button {
  flex: 1;
  min-width: 160rpx;
  border: none;
  border-radius: 48rpx;
  padding: 24rpx 20rpx; /* 减少按钮高度 */
  font-size: 28rpx;
  font-weight: 600;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 16rpx rgba(102, 126, 234, 0.3);
}

.action-button.secondary {
  background: #ffffff;
  color: #667eea;
  border: 2rpx solid #667eea;
}

.action-button.danger {
  background: #ff4757;
  color: #ffffff;
  box-shadow: 0 8rpx 16rpx rgba(255, 71, 87, 0.3);
}

.action-button:active {
  transform: scale(0.95);
}

.action-button.primary:active {
  box-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.2);
}

.action-button.danger:active {
  box-shadow: 0 4rpx 8rpx rgba(255, 71, 87, 0.2);
}
