<!--pages/bluetooth-connect/bluetooth-connect.wxml-->
<t-navbar title="蓝牙连接" left-arrow />
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 设备信息卡片 -->
    <view class="device-card">
      <view class="device-header">
        <view class="device-icon {{isConnected ? 'connected' : ''}}">
          <image src="/images/mobile.svg" class="device-image" />
          <view class="connection-status {{isConnected ? 'active' : ''}}"></view>
        </view>
        <view class="device-info">
          <view class="device-name">{{deviceName}}</view>
          <view class="device-id">{{deviceId}}</view>
          <view class="connection-text {{isConnected ? 'connected' : 'disconnected'}}">
            {{isConnected ? '已连接' : '未连接'}}
          </view>
        </view>
      </view>

      <!-- 连接状态 -->
      <view class="connection-progress" wx:if="{{isConnecting}}">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{connectProgress}}%"></view>
        </view>
        <view class="progress-text">{{connectStatusText}}</view>
      </view>
    </view>

    <!-- 数据显示区域 -->
    <view class="data-section" wx:if="{{isConnected}}">
      <view class="section-header">
        <text class="section-title">实时数据</text>
        <view class="data-status {{dataReceiving ? 'receiving' : ''}}">
          <view class="status-dot"></view>
          <text class="status-text">{{dataReceiving ? '接收中' : '等待数据'}}</text>
        </view>
      </view>

      <!-- 数据卡片列表 -->
      <view class="data-cards">
        <view class="data-card" wx:for="{{dataList}}" wx:key="timestamp">
          <view class="data-header">
            <text class="data-time">{{item.timeStr}}</text>
            <text class="data-type">{{item.type}}</text>
          </view>
          <view class="data-content">
            <text class="data-text" wx:if="{{item.type === 'text'}}">{{item.data}}</text>
            <view class="data-hex" wx:if="{{item.type === 'hex'}}">
              <text class="hex-label">HEX:</text>
              <text class="hex-value">{{item.hexData}}</text>
            </view>
            <view class="data-json" wx:if="{{item.type === 'json'}}">
              <text class="json-data">{{item.jsonData}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 数据统计 -->
      <view class="data-stats">
        <view class="stat-item">
          <text class="stat-value">{{receivedCount}}</text>
          <text class="stat-label">已接收</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{dataRate}}</text>
          <text class="stat-label">数据率/s</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{connectionTime}}</text>
          <text class="stat-label">连接时长</text>
        </view>
      </view>
    </view>

    <!-- 空数据状态 -->
    <view class="empty-data" wx:if="{{isConnected && dataList.length === 0}}">
      <view class="empty-icon">
        <image src="/images/file.svg" class="empty-image" />
      </view>
      <view class="empty-text">暂无数据</view>
      <view class="empty-desc">等待设备发送数据...</view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-button secondary" bindtap="clearData" wx:if="{{isConnected && dataList.length > 0}}">
        清空数据
      </button>
      <button class="action-button danger" bindtap="disconnect" wx:if="{{isConnected}}">
        断开连接
      </button>
      <button class="action-button primary" bindtap="connect" wx:if="{{!isConnected && !isConnecting}}">
        连接设备
      </button>
      <button class="action-button secondary" bindtap="goBack">
        返回搜索
      </button>
    </view>
  </view>
</scroll-view>