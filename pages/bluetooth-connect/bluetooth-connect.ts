// pages/bluetooth-connect/bluetooth-connect.ts

interface DataItem {
  timestamp: number;
  timeStr: string;
  type: 'text' | 'hex' | 'json';
  data: string;
  hexData?: string;
  jsonData?: string;
}

Page({
  data: {
    deviceId: '',
    deviceName: '',
    isConnected: false,
    isConnecting: false,
    connectProgress: 0,
    connectStatusText: '',

    // 数据相关
    dataList: [] as DataItem[],
    dataReceiving: false,
    receivedCount: 0,
    dataRate: 0,
    connectionTime: '00:00:00',

    // 定时器
    connectionTimer: null as number | null,
    dataRateTimer: null as number | null,

    // 连接开始时间
    connectStartTime: 0,
    lastDataCount: 0,
  },

  onLoad(options: any) {
    console.log('页面参数:', options);

    if (options.deviceId && options.deviceName) {
      this.setData({
        deviceId: options.deviceId,
        deviceName: decodeURIComponent(options.deviceName),
      });

      // 自动开始连接
      setTimeout(() => {
        this.connect();
      }, 500);
    }
  },

  onUnload() {
    this.cleanup();
  },

  // 连接设备
  connect() {
    if (this.data.isConnecting || this.data.isConnected) {
      return;
    }

    this.setData({
      isConnecting: true,
      connectProgress: 0,
      connectStatusText: '正在连接...',
    });

    this.updateConnectProgress(20, '初始化连接');

    // 连接到设备
    wx.createBLEConnection({
      deviceId: this.data.deviceId,
      success: (res) => {
        console.log('连接成功', res);
        this.updateConnectProgress(60, '连接成功，获取服务');
        this.getServices();
      },
      fail: (err) => {
        console.error('连接失败', err);
        this.handleConnectionError(err);
      },
    });
  },

  // 获取服务
  getServices() {
    wx.getBLEDeviceServices({
      deviceId: this.data.deviceId,
      success: (res) => {
        console.log('获取服务成功', res);
        this.updateConnectProgress(80, '获取特征值');

        if (res.services.length > 0) {
          // 使用第一个服务
          const serviceId = res.services[0].uuid;
          this.getCharacteristics(serviceId);
        } else {
          this.handleConnectionError({ errMsg: '未找到可用服务' });
        }
      },
      fail: (err) => {
        console.error('获取服务失败', err);
        this.handleConnectionError(err);
      },
    });
  },

  // 获取特征值
  getCharacteristics(serviceId: string) {
    wx.getBLEDeviceCharacteristics({
      deviceId: this.data.deviceId,
      serviceId: serviceId,
      success: (res) => {
        console.log('获取特征值成功', res);
        this.updateConnectProgress(90, '启用通知');

        // 找到支持notify的特征值
        const notifyCharacteristic = res.characteristics.find(
          (char) => char.properties.notify || char.properties.indicate
        );

        if (notifyCharacteristic) {
          this.enableNotification(serviceId, notifyCharacteristic.uuid);
        } else {
          // 如果没有notify特征值，也算连接成功，但不能接收数据
          this.onConnectionSuccess();
        }
      },
      fail: (err) => {
        console.error('获取特征值失败', err);
        this.handleConnectionError(err);
      },
    });
  },

  // 启用通知
  enableNotification(serviceId: string, characteristicId: string) {
    wx.notifyBLECharacteristicValueChange({
      deviceId: this.data.deviceId,
      serviceId: serviceId,
      characteristicId: characteristicId,
      state: true,
      success: (res) => {
        console.log('启用通知成功', res);
        this.updateConnectProgress(100, '连接完成');
        this.setupDataListener();
        this.onConnectionSuccess();
      },
      fail: (err) => {
        console.error('启用通知失败', err);
        // 即使通知启用失败，也算连接成功
        this.onConnectionSuccess();
      },
    });
  },

  // 连接成功
  onConnectionSuccess() {
    this.setData({
      isConnected: true,
      isConnecting: false,
      connectProgress: 100,
      connectStatusText: '连接成功',
      connectStartTime: Date.now(),
    });

    this.startConnectionTimer();
    this.startDataRateTimer();

    wx.showToast({
      title: '连接成功',
      icon: 'success',
    });

    // 监听连接状态变化
    wx.onBLEConnectionStateChange((res) => {
      console.log('连接状态变化', res);
      if (!res.connected) {
        this.onConnectionLost();
      }
    });
  },

  // 连接丢失
  onConnectionLost() {
    this.setData({
      isConnected: false,
      dataReceiving: false,
    });

    this.stopTimers();

    wx.showToast({
      title: '连接已断开',
      icon: 'none',
    });
  },

  // 更新连接进度
  updateConnectProgress(progress: number, statusText: string) {
    this.setData({
      connectProgress: progress,
      connectStatusText: statusText,
    });
  },

  // 处理连接错误
  handleConnectionError(err: any) {
    this.setData({
      isConnecting: false,
      connectProgress: 0,
      connectStatusText: '连接失败',
    });

    let message = '连接失败';
    if (err.errMsg) {
      message = err.errMsg;
    }

    wx.showModal({
      title: '连接失败',
      content: message,
      showCancel: false,
    });
  },

  // 设置数据监听
  setupDataListener() {
    wx.onBLECharacteristicValueChange((res) => {
      console.log('收到数据', res);
      this.handleReceivedData(res.value);
    });
  },

  // 处理接收到的数据
  handleReceivedData(arrayBuffer: ArrayBuffer) {
    this.setData({
      dataReceiving: true,
      receivedCount: this.data.receivedCount + 1,
    });

    const now = new Date();
    const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now
      .getMinutes()
      .toString()
      .padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

    // 转换数据格式
    const uint8Array = new Uint8Array(arrayBuffer);
    const hexData = Array.from(uint8Array)
      .map((byte) => byte.toString(16).padStart(2, '0').toUpperCase())
      .join(' ');

    // 尝试转换为文本
    let textData = '';
    try {
      // 使用微信小程序的方式处理文本转换
      const uint8Array = new Uint8Array(arrayBuffer);
      textData = String.fromCharCode(...uint8Array);
    } catch (e) {
      textData = hexData;
    }

    // 判断数据类型
    let dataType: 'text' | 'hex' | 'json' = 'text';
    let jsonData = '';

    try {
      const parsed = JSON.parse(textData);
      dataType = 'json';
      jsonData = JSON.stringify(parsed, null, 2);
    } catch (e) {
      if (/^[\x20-\x7E\s]*$/.test(textData)) {
        dataType = 'text';
      } else {
        dataType = 'hex';
      }
    }

    const dataItem: DataItem = {
      timestamp: now.getTime(),
      timeStr: timeStr,
      type: dataType,
      data: textData,
      hexData: hexData,
      jsonData: jsonData,
    };

    // 添加到列表开头（最新的在前面）
    const newDataList = [dataItem, ...this.data.dataList];

    // 限制列表长度，避免内存溢出
    if (newDataList.length > 100) {
      newDataList.splice(100);
    }

    this.setData({
      dataList: newDataList,
    });

    // 1秒后停止接收动画
    setTimeout(() => {
      this.setData({ dataReceiving: false });
    }, 1000);
  },

  // 断开连接
  disconnect() {
    if (!this.data.isConnected) {
      return;
    }

    wx.closeBLEConnection({
      deviceId: this.data.deviceId,
      success: (res) => {
        console.log('断开连接成功', res);
        this.onConnectionLost();
      },
      fail: (err) => {
        console.error('断开连接失败', err);
        // 即使断开失败，也更新状态
        this.onConnectionLost();
      },
    });
  },

  // 清空数据
  clearData() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有接收的数据吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            dataList: [],
            receivedCount: 0,
          });
          wx.showToast({
            title: '数据已清空',
            icon: 'success',
          });
        }
      },
    });
  },

  // 返回搜索页面
  goBack() {
    wx.navigateBack({
      fail: () => {
        wx.navigateTo({
          url: '/pages/bluetooth-search/bluetooth-search',
        });
      },
    });
  },

  // 开始连接时间计时器
  startConnectionTimer() {
    const timer = setInterval(() => {
      if (this.data.isConnected && this.data.connectStartTime > 0) {
        const duration = Date.now() - this.data.connectStartTime;
        const hours = Math.floor(duration / 3600000);
        const minutes = Math.floor((duration % 3600000) / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);

        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes
          .toString()
          .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        this.setData({ connectionTime: timeStr });
      }
    }, 1000);

    this.setData({ connectionTimer: timer });
  },

  // 开始数据率计时器
  startDataRateTimer() {
    const timer = setInterval(() => {
      const currentCount = this.data.receivedCount;
      const rate = currentCount - this.data.lastDataCount;

      this.setData({
        dataRate: rate,
        lastDataCount: currentCount,
      });
    }, 1000);

    this.setData({ dataRateTimer: timer });
  },

  // 停止所有计时器
  stopTimers() {
    if (this.data.connectionTimer) {
      clearInterval(this.data.connectionTimer);
      this.setData({ connectionTimer: null });
    }

    if (this.data.dataRateTimer) {
      clearInterval(this.data.dataRateTimer);
      this.setData({ dataRateTimer: null });
    }
  },

  // 清理资源
  cleanup() {
    this.stopTimers();

    // 断开连接
    if (this.data.isConnected) {
      wx.closeBLEConnection({
        deviceId: this.data.deviceId,
      });
    }
  },
});
