<!--device-list.wxml-->
<t-navbar title="设备列表" />

<view class="device-list-container">
  <!-- 搜索栏 -->
  <view class="search-section" style="top: {{searchSectionTop}}px;">
    <view class="search-box">
      <image class="search-left-icon" src="/images/scan.png" mode="aspectFit"></image>
      <input class="search-input" placeholder="请输入电池编码" value="{{searchValue}}" bindinput="onSearchChange"
        placeholder-style="color: #c5c5c5;" />
      <image class="search-right-icon" src="/images/search.svg" mode="aspectFit" bindtap="onSearch"></image>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs-section" style="margin-top: {{tabsSectionMarginTop}}px;">
    <t-tabs value="{{activeTab}}" bind:change="onTabChange" space-evenly="{{true}}" theme="tag">
      <t-tab-panel label="全部·{{totalCount}}" value="all"></t-tab-panel>
      <t-tab-panel label="在线·{{onlineCount}}" value="online"></t-tab-panel>
      <t-tab-panel label="离线·{{offlineCount}}" value="offline"></t-tab-panel>
    </t-tabs>
  </view>

  <!-- 设备列表 -->
  <view class="device-list">
    <view class="device-item" wx:for="{{deviceList}}" wx:key="id" wx:for-item="device" data-device="{{device}}"
      data-index="{{index}}" catchtap="onDeviceTap">
      <view class="device-card">
        <!-- 状态标签 - 右上角 -->
        <view class="status-tag {{device.isOnline ? 'online' : 'offline'}}">
          <view class="status-dot {{device.isOnline ? 'online' : 'offline'}}"></view>
          <text class="status-text">{{device.isOnline ? '在线' : '离线'}}</text>
        </view>

        <!-- 左侧电池图标 -->
        <view class="battery-container">
          <view class="battery-wrapper {{device.batteryLevel>=20  ? 'online' : 'offline'}}">
            <view class="battery-body {{device.batteryLevel>=20 ? 'good' : 'bad'}}">
              <view class="battery-head"></view>
              <view class="battery-fill" style="height: {{device.batteryLevel}}%;"></view>
              <view class="battery-level" style="top: {{100 - device.batteryLevel+6}}%;">{{device.batteryLevel}}</view>
            </view>
          </view>
          <view class="line" />
        </view>

        <!-- 设备信息 -->
        <view class="device-info">
          <view class="device-code">{{device.code}}</view>

          <view class="device-details">
            <view class="detail-row">
              <text class="detail-text">电压 <text class="detail-value">{{device.voltage}}</text></text>
              <text class="detail-text">GPS时间 <text class="detail-value">{{device.gpsTime}}</text></text>
            </view>


            <view class="detail-row bg-gray">
              <text class="detail-text bg-gray">租赁时间 <text class="detail-value">{{device.rentTime}}</text></text>
            </view>


            <view class="detail-row bg-gray">
              <text class="detail-text bg-gray" style="margin-right: 0;">质保时间 <text
                  class="detail-value">{{device.warrantyTime}}</text></text>
            </view>
          </view>
        </view>


      </view>
      <!-- 操作按钮 -->
      <!-- <view class="action-buttons">
        <view class="action-btn" data-device="{{device}}" bind:tap="onLocationTap">
          <image class="action-icon" src="/images/location.png" mode="aspectFit"></image>
          <text class="action-text">定位</text>
        </view>

        <view class="action-btn" data-device="{{device}}" bind:tap="onBMSTap">
          <image class="action-icon" src="/images/bat.png" mode="aspectFit"></image>
          <text class="action-text">BMS</text>
        </view>

        <view class="action-btn" data-device="{{device}}" bind:tap="onTrackTap">
          <image class="action-icon" src="/images/route.png" mode="aspectFit"></image>
          <text class="action-text">轨迹</text>
        </view>
      </view> -->
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{deviceList.length === 0}}">
      <text class="empty-text">暂无设备数据</text>
    </view>
  </view>
</view>