// device-list.ts
import { onPageShow } from '../../utils/tabbar.js';

interface DeviceInfo {
  id: string;
  code: string;
  voltage: string;
  gpsTime: string;
  rentTime: string;
  warrantyTime: string;
  batteryLevel: number;
  isOnline: boolean;
}

Page({
  data: {
    searchValue: '',
    activeTab: 'all',
    totalCount: 0,
    onlineCount: 0,
    offlineCount: 0,
    deviceList: [] as DeviceInfo[],
    allDevices: [] as DeviceInfo[],
    navBarHeight: 0, // 导航栏高度
    searchSectionTop: 0, // 搜索栏top值
    tabsSectionMarginTop: 0, // 标签页margin-top值
  },

  onLoad() {
    this.calculateNavBarHeight();
    this.initializeData();
  },

  onShow() {
    console.log('设备列表页面显示');
    // 使用工具函数更新自定义tabbar状态
    onPageShow(this);
  },

  // 计算导航栏高度
  calculateNavBarHeight() {
    const systemInfo = wx.getSystemInfoSync();
    const { statusBarHeight = 0 } = systemInfo;

    // 导航栏高度 = 状态栏高度 + 导航栏内容高度(一般是44px)
    const navBarHeight = statusBarHeight + 44 - 4;

    // 搜索栏top = 导航栏高度
    const searchSectionTop = navBarHeight;

    // 标签页margin-top = 导航栏高度 + 搜索栏高度(大约64px)
    const tabsSectionMarginTop = 54 + 5;

    this.setData({
      navBarHeight,
      searchSectionTop,
      tabsSectionMarginTop,
    });
  },

  // 初始化数据
  initializeData() {
    const mockDevices: DeviceInfo[] = [
      {
        id: '1',
        code: '052124004418',
        voltage: '28.2V',
        gpsTime: '200',
        rentTime: '06-21 14:35',
        warrantyTime: '24小时',
        batteryLevel: 85,
        isOnline: true,
      },
      {
        id: '2',
        code: '052124004418',
        voltage: '28.2V',
        gpsTime: '200',
        rentTime: '06-21 14:35',
        warrantyTime: '24小时',
        batteryLevel: 17,
        isOnline: false,
      },
      {
        id: '3',
        code: '052124004418',
        voltage: '28.2V',
        gpsTime: '200',
        rentTime: '06-21 14:35',
        warrantyTime: '24小时',
        batteryLevel: 46,
        isOnline: true,
      },
      {
        id: '4',
        code: '052124004419',
        voltage: '27.5V',
        gpsTime: '180',
        rentTime: '06-21 12:20',
        warrantyTime: '24小时',
        batteryLevel: 23,
        isOnline: false,
      },
      {
        id: '5',
        code: '052124004420',
        voltage: '28.5V',
        gpsTime: '220',
        rentTime: '06-21 16:15',
        warrantyTime: '24小时',
        batteryLevel: 92,
        isOnline: true,
      },
      {
        id: '6',
        code: '052124004421',
        voltage: '28.1V',
        gpsTime: '195',
        rentTime: '06-21 11:45',
        warrantyTime: '24小时',
        batteryLevel: 78,
        isOnline: true,
      },
    ];

    // 计算统计数据
    const onlineDevices = mockDevices.filter((device) => device.isOnline);
    const offlineDevices = mockDevices.filter((device) => !device.isOnline);

    this.setData({
      allDevices: mockDevices,
      deviceList: mockDevices,
      totalCount: 478, // 使用截图中的数据
      onlineCount: 232,
      offlineCount: 115,
    });
  },

  // 搜索输入变化 - 适配自定义搜索框
  onSearchChange(event: any) {
    const { value } = event.detail;
    this.setData({ searchValue: value });
    this.filterDevices();
  },

  // 点击搜索按钮
  onSearch() {
    this.filterDevices();
  },

  // 标签页切换
  onTabChange(event: any) {
    const { value } = event.detail;
    this.setData({ activeTab: value });
    this.filterDevices();
  },

  // 过滤设备列表
  filterDevices() {
    const { searchValue, activeTab, allDevices } = this.data;
    let filteredDevices = allDevices;

    // 根据搜索关键词过滤
    if (searchValue.trim()) {
      filteredDevices = filteredDevices.filter((device) =>
        device.code.toLowerCase().includes(searchValue.toLowerCase())
      );
    }

    // 根据标签页过滤
    switch (activeTab) {
      case 'online':
        filteredDevices = filteredDevices.filter((device) => device.isOnline);
        break;
      case 'offline':
        filteredDevices = filteredDevices.filter((device) => !device.isOnline);
        break;
      default:
        // 全部，不需要额外过滤
        break;
    }

    this.setData({ deviceList: filteredDevices });
  },

  // 定位按钮点击
  onLocationTap(event: any) {
    const { device } = event.currentTarget.dataset;
    wx.showToast({
      title: `定位设备: ${device.code}`,
      icon: 'none',
      duration: 2000,
    });
    console.log('定位设备:', device);
  },

  // BMS按钮点击
  onBMSTap(event: any) {
    const { device } = event.currentTarget.dataset;
    wx.showToast({
      title: `查看BMS: ${device.code}`,
      icon: 'none',
      duration: 2000,
    });
    console.log('查看BMS:', device);
  },

  // 轨迹按钮点击
  onTrackTap(event: any) {
    const { device } = event.currentTarget.dataset;
    wx.showToast({
      title: `查看轨迹: ${device.code}`,
      icon: 'none',
      duration: 2000,
    });
    console.log('查看轨迹:', device);
  },

  onDeviceTap(event: any) {
    const { device } = event.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/tabbar/detail/detail?deviceId=${device.id}`,
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '设备列表',
      path: '/pages/device-list/device-list',
    };
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 模拟刷新数据
    setTimeout(() => {
      this.initializeData();
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500,
      });
    }, 1000);
  },
});
