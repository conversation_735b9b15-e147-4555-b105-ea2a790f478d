<!--pages/schedule-heat/schedule-heat.wxml-->
<t-navbar title="预约加热" left-arrow bind:go-back="onGoBack" />

<scroll-view class="page-container" scroll-y type="list">
  <!-- 主要内容区域 -->
  <view class="main-content">

    <!-- 加热记录标题 -->
    <view class="section-header">
      <view class="section-header-line"></view>
      <image src="/images/hotting.svg" mode="aspectFit" style="width: 44rpx; height: 44rpx;" />
      <text class="section-title">加热记录</text>
      <view class="section-header-line"></view>
    </view>

    <!-- 加热记录列表 -->
    <view class="heat-records">
      <view class="record-item" wx:for="{{heatRecords}}" wx:key="id">
        <view class="record-time">
          <t-icon name="time" size="32rpx" color="#001a57" />
          <text class="time-text">{{item.date}} {{item.time}}</text>
        </view>
        <view class="record-status">
          <text class="status-text status-{{item.type}}">{{item.status}}</text>
        </view>
      </view>
    </view>

  </view>

  <!-- 底部按钮区域 -->
  <view class="bottom-actions">
    <t-button variant="outline" theme="primary" shape="round" size="large" class="action-btn immediate-btn"
      bind:tap="onImmediateHeat">
      立即加热
    </t-button>
    <t-button variant="base" theme="primary" shape="round" size="large" class="action-btn schedule-btn"
      bind:tap="onScheduleHeat">
      预约加热
    </t-button>
  </view>
</scroll-view>

<!-- 预约加热弹窗 -->
<t-popup visible="{{showSchedulePopup}}" placement="bottom" bind:visible-change="onSchedulePopupChange">
  <view class="schedule-popup">
    <!-- 弹窗标题 -->
    <view class="popup-header">
      <text class="popup-title">预约加热</text>
    </view>

    <!-- 弹窗内容 -->
    <view class="popup-content">
      <!-- 频率选择 -->
      <view class="schedule-type-section">
        <t-radio-group value="{{scheduleType}}" bind:change="onScheduleTypeChange">
          <view class="radio-item">
            <t-radio value="once" label="指定日期" />
          </view>
          <view class="radio-item">
            <t-radio value="weekly" label="每周" />
          </view>
          <view class="radio-item">
            <t-radio value="monthly" label="每月" />
          </view>
          <view class="radio-item">
            <t-radio value="daily" label="每天" />
          </view>
        </t-radio-group>
      </view>

      <!-- 时间选择器 - 指定日期时显示 -->
      <view class="datetime-picker-section" wx:if="{{scheduleType === 'once'}}">
        <t-date-time-picker usePopup="{{false}}" visible="{{false}}" mode="minute" bind:change="onDateTimeChange"
          bind:pick="onDateTimePick" bind:confirm="onDateTimeConfirm" defaultValue="{{date}}" start="{{start}}"
          end="{{end}}" header="{{false}}" />
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="popup-footer">
      <t-button variant="outline" theme="primary" shape="round" size="large" class="popup-btn cancel-btn"
        bind:tap="onCancelSchedule">
        取消
      </t-button>
      <t-button variant="base" theme="primary" shape="round" size="large" class="popup-btn confirm-btn"
        bind:tap="onConfirmSchedule">
        确认
      </t-button>
    </view>
  </view>
</t-popup>