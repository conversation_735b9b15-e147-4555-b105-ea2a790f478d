// pages/schedule-heat/schedule-heat.ts
interface HeatRecord {
  id: number;
  date: string;
  time: string;
  status: string;
  type: string;
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    showSchedulePopup: false,
    scheduleType: 'once', // once: 指定日期, weekly: 每周, monthly: 每月, daily: 每天
    selectedDateTime: new Date().getTime(), // 使用时间戳作为默认值
    date: new Date('2021-12-23').getTime(), // 支持时间戳传入

    // 指定选择区间起始值
    start: '2000-01-01 00:00:00',
    end: '2030-09-09 12:12:12',
    // 模拟加热记录数据
    heatRecords: [
      {
        id: 1,
        date: '2025-06-23',
        time: '14:34:09',
        status: '定时：每周二',
        type: 'weekly',
      },
      {
        id: 2,
        date: '2025-06-23',
        time: '14:34:09',
        status: '定时：每月6号',
        type: 'monthly',
      },
      {
        id: 3,
        date: '2025-06-23',
        time: '14:34:09',
        status: '定时：每天',
        type: 'daily',
      },
      {
        id: 4,
        date: '2025-06-23',
        time: '14:34:09',
        status: '即时加热',
        type: 'immediate',
      },
      {
        id: 5,
        date: '2025-06-23',
        time: '14:34:09',
        status: '指定日期',
        type: 'once',
      },
    ] as HeatRecord[],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 设置默认时间为当前时间
    this.setData({
      selectedDateTime: new Date().getTime(),
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新加热记录
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '预约加热 - 天储BMS',
      path: '/pages/schedule-heat/schedule-heat',
    };
  },

  /**
   * 返回按钮点击
   */
  onGoBack() {
    wx.navigateBack();
  },

  /**
   * 立即加热
   */
  onImmediateHeat() {
    wx.showModal({
      title: '确认操作',
      content: '确定要立即开始加热吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '启动中...',
          });

          // 模拟加热启动
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '加热启动成功',
              icon: 'success',
            });

            // 添加新的记录到列表
            this.addHeatRecord({
              id: Date.now(),
              date: this.formatDate(new Date()),
              time: this.formatTime(new Date()),
              status: '即时加热',
              type: 'immediate',
            });
          }, 2000);
        }
      },
    });
  },

  /**
   * 预约加热
   */
  onScheduleHeat() {
    this.setData({
      showSchedulePopup: true,
    });
  },

  /**
   * 预约弹窗显示状态变化
   */
  onSchedulePopupChange(event: any) {
    this.setData({
      showSchedulePopup: event.detail.visible,
    });
  },

  /**
   * 预约类型选择变化
   */
  onScheduleTypeChange(event: any) {
    console.log('选择类型变化:', event.detail.value);
    this.setData({
      scheduleType: event.detail.value,
    });
  },

  /**
   * 时间选择器变化
   */
  onDateTimeChange(event: any) {
    console.log('时间选择器变化:', event.detail);
    this.setData({
      selectedDateTime: event.detail.value,
    });
  },

  /**
   * 时间选择器选中
   */
  onDateTimePick(event: any) {
    console.log('时间选择器选中:', event.detail);
    this.setData({
      selectedDateTime: event.detail.value,
    });
  },

  /**
   * 时间选择器取消
   */
  onDateTimeCancel(event: any) {
    console.log('时间选择器取消:', event.detail);
  },

  /**
   * 时间选择器确认
   */
  onDateTimeConfirm(event: any) {
    console.log('时间选择器确认:', event.detail);
    this.setData({
      selectedDateTime: event.detail.value,
    });
  },

  /**
   * 取消预约
   */
  onCancelSchedule() {
    this.setData({
      showSchedulePopup: false,
    });
  },

  /**
   * 确认预约
   */
  onConfirmSchedule() {
    const { scheduleType, selectedDateTime } = this.data;

    wx.showLoading({
      title: '设置中...',
    });

    // 模拟设置预约
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '预约设置成功',
        icon: 'success',
      });

      // 添加新的预约记录
      let statusText = '';
      switch (scheduleType) {
        case 'once':
          statusText = '指定日期';
          break;
        case 'weekly':
          statusText = '定时：每周';
          break;
        case 'monthly':
          statusText = '定时：每月';
          break;
        case 'daily':
          statusText = '定时：每天';
          break;
      }

      this.addHeatRecord({
        id: Date.now(),
        date: this.formatDate(new Date()),
        time: this.formatTime(new Date()),
        status: statusText,
        type: scheduleType,
      });

      this.setData({
        showSchedulePopup: false,
      });
    }, 1500);
  },

  /**
   * 添加加热记录
   */
  addHeatRecord(record: HeatRecord) {
    const records = [record, ...this.data.heatRecords];
    this.setData({
      heatRecords: records,
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化时间
   */
  formatTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  },
});
