/* pages/schedule-heat/schedule-heat.less */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f6f7;
}

.page-container {
  flex: 1;
  overflow-y: hidden;
}

/* 主要内容区域 */
.main-content {
  padding: 40rpx 50rpx 180rpx;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  padding-left: 50rpx;
  padding-right: 50rpx;
}

/* 章节标题 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.section-title {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 28rpx;
  line-height: 1.57;
  color: #191919;
}

.section-header-line {
  flex: 1;
  height: 2rpx;
  background-image: linear-gradient(to right, #d8d8d8 50%, transparent 50%);
  background-size: 16rpx 2rpx;
  background-repeat: repeat-x;
}

/* 加热记录列表 */
.heat-records {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

.record-item {
  background: linear-gradient(90deg, #ffffff 0%, #ffffff 70%, #fff0e0 100%);
  border-radius: 16rpx;
  padding: 28rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0px 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.record-time {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.time-text {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 32rpx;
  line-height: 1.5;
  color: #191919;
}

.record-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 24rpx;
  line-height: 1.17;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

/* 不同状态的颜色 - 严格按照 Figma 设计 */
.status-weekly,
.status-monthly,
.status-daily {
  background: #fff1e9;
  color: #e37318;
}

.status-immediate {
  background: #e8f5e8;
  color: #00c851;
}

.status-once {
  background: #fff1e9;
  color: #e37318;
}

/* 底部按钮区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 32rpx 48rpx;
  padding-bottom: 68rpx; /* 32rpx + 36rpx 安全区域 */
  display: flex;
  gap: 60rpx;
  box-shadow: 0px -2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.action-btn {
  flex: 1;
  height: 96rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  border-radius: 100rpx !important;
}

/* 预约加热弹窗样式 */
.schedule-popup {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.popup-header {
  padding: 32rpx 32rpx 0;
  text-align: center;
}

.popup-title {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 36rpx;
  line-height: 1.44;
  color: #191919;
}

.popup-content {
  padding: 60rpx 0 0;
}

/* 频率选择区域 */
.schedule-type-section {
  padding: 0 32rpx;
}

/* 单选按钮样式 */
.radio-item {
}

.radio-item:last-child {
  .t-radio__border {
    height: 0 !important;
  }
}
.t-radio--block {
  padding-left: 20rpx !important;
}

/* 时间选择器区域 */
.datetime-picker-section {
  margin-top: 4rpx;
  padding: 20rpx 0;
  height: 200px;
}

/* 弹窗底部按钮 */
.popup-footer {
  padding: 32rpx 48rpx;
  padding-bottom: 68rpx; /* 32rpx + 36rpx 安全区域 */
  display: flex;
  gap: 60rpx;
}

.popup-btn {
  flex: 1;
  height: 96rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  border-radius: 100rpx !important;
}

/* 时间选择器样式 */
.t-date-time-picker {
  background: #ffffff;
}

.t-date-time-picker__content {
  background: #ffffff;
  border-radius: 24rpx;
  min-height: 480rpx;
}

.t-picker-item {
  font-size: 32rpx !important;
  color: #191919 !important;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
}

.t-picker-item--active {
  color: #191919 !important;
  font-weight: 600 !important;
}

.t-picker__indicator {
  background: #f3f3f3;
  border-radius: 12rpx;
}

.t-picker__mask-top,
.t-picker__mask-bottom {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

/* 按钮样式覆盖 */
.t-button--theme-primary {
  background-color: #0052d9 !important;
  border-color: #0052d9 !important;
}

.t-button--variant-outline.t-button--theme-primary {
  color: #0052d9 !important;
  border-color: #0052d9 !important;
  background-color: transparent !important;
}

.t-button--variant-outline.t-button--theme-primary:hover,
.t-button--variant-outline.t-button--theme-primary:active {
  background-color: rgba(0, 82, 217, 0.05) !important;
}
