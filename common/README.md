# 登录功能使用说明

本文档说明如何在小程序中使用自动登录功能和相关的认证管理。

## 功能特性

- ✅ 小程序启动时自动登录
- ✅ Token 自动管理和存储
- ✅ 登录状态全局管理
- ✅ 事件驱动的登录状态更新
- ✅ 用户注册流程支持
- ✅ 页面级登录状态检查
- ✅ 统一的错误处理

## 文件结构

```
common/
├── auth.ts          # 核心认证服务
├── loginHelper.ts   # 页面登录辅助工具
├── storage.ts       # 本地存储管理
├── token.ts         # Token 类定义
└── README.md        # 使用说明
```

## 核心流程

### 1. 自动登录流程

小程序启动时会自动执行以下流程：

```
启动小程序
    ↓
检查本地 Token
    ↓
Token 有效? ──→ 是 ──→ 验证用户信息 ──→ 登录成功
    ↓
    否
    ↓
调用微信登录获取 code
    ↓
调用后端登录接口
    ↓
返回 Token? ──→ 是 ──→ 保存 Token ──→ 获取用户信息 ──→ 登录成功
    ↓
    否
    ↓
返回会话信息 ──→ 需要用户注册
```

### 2. 事件系统

系统通过事件总线通知各个页面登录状态的变化：

- `loginSuccess` - 登录成功
- `loginFailed` - 登录失败
- `needRegister` - 需要用户注册
- `loginStatusUpdate` - 登录状态更新
- `logout` - 用户登出
- `userInfoUpdate` - 用户信息更新

## 使用方法

### 1. 在页面中检查登录状态

```javascript
// pages/example/example.js
import loginHelper from '../../common/loginHelper';

Page({
  onLoad() {
    // 初始化登录状态检查
    loginHelper.initPageLogin(this, {
      requireLogin: true, // 需要登录才能访问
      onLoginSuccess: (userInfo) => {
        console.log('用户已登录:', userInfo);
        this.setData({ userInfo });
      },
      onLoginFailed: () => {
        console.log('登录失败');
        wx.showToast({
          title: '请先登录',
          icon: 'none',
        });
      },
      onNeedRegister: (sessionData) => {
        console.log('需要注册:', sessionData);
        // 跳转到注册页面
        wx.navigateTo({
          url: '/pages/register/register',
        });
      },
    });
  },

  // 手动登录
  async handleLogin() {
    const success = await loginHelper.manualLogin();
    if (success) {
      // 登录成功后的处理
      this.loadData();
    }
  },

  // 登出
  handleLogout() {
    loginHelper.logout();
  },

  // 检查登录状态后执行操作
  handleSecureAction() {
    loginHelper.checkLoginAndExecute(() => {
      // 需要登录的操作
      console.log('执行安全操作');
    });
  },
});
```

### 2. 用户注册页面

```javascript
// pages/register/register.js
import loginHelper from '../../common/loginHelper';

Page({
  data: {
    nickname: '',
    avatar: '',
    mobile: '',
  },

  onLoad() {
    // 获取微信用户信息
    this.getUserProfile();
  },

  // 获取用户头像和昵称
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          nickname: res.userInfo.nickName,
          avatar: res.userInfo.avatarUrl,
        });
      },
    });
  },

  // 获取手机号
  getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      this.setData({
        mobile: e.detail.phoneNumber,
      });
    }
  },

  // 提交注册
  async handleRegister() {
    const { nickname, avatar, mobile } = this.data;

    if (!nickname || !avatar || !mobile) {
      wx.showToast({
        title: '请完善信息',
        icon: 'none',
      });
      return;
    }

    const success = await loginHelper.registerUser({
      nickname,
      avatar,
      mobile,
    });

    if (success) {
      // 注册成功，返回上一页或跳转到首页
      wx.navigateBack({
        fail: () => {
          wx.switchTab({ url: '/pages/tabbar/index/index' });
        },
      });
    }
  },
});
```

### 3. 在组件中使用

```javascript
// components/user-info/user-info.js
import loginHelper from '../../common/loginHelper';

Component({
  data: {
    userInfo: null,
    isLoggedIn: false,
  },

  lifetimes: {
    attached() {
      // 检查登录状态
      const loginStatus = loginHelper.getLoginStatus();
      this.setData({
        userInfo: loginStatus.userInfo,
        isLoggedIn: loginStatus.isLoggedIn,
      });

      // 监听登录状态变化
      this.setupLoginListener();
    },

    detached() {
      // 移除事件监听（loginHelper 会自动处理）
    },
  },

  methods: {
    setupLoginListener() {
      loginHelper.initPageLogin(this, {
        onLoginSuccess: (userInfo) => {
          this.setData({
            userInfo: userInfo,
            isLoggedIn: true,
          });
        },
        onLoginFailed: () => {
          this.setData({
            userInfo: null,
            isLoggedIn: false,
          });
        },
      });
    },

    // 刷新用户信息
    async refreshUserInfo() {
      const success = await loginHelper.refreshUserInfo();
      if (success) {
        const loginStatus = loginHelper.getLoginStatus();
        this.setData({
          userInfo: loginStatus.userInfo,
        });
      }
    },
  },
});
```

### 4. 直接使用认证服务

```javascript
// 某个工具文件或页面中
import authService from '../../common/auth';

// 检查是否已登录
if (authService.isLoggedIn()) {
  console.log('用户已登录');
  const userInfo = authService.getCurrentUser();
  console.log('用户信息:', userInfo);
}

// 手动登出
authService.logout();

// 获取会话数据（用于注册）
const sessionData = authService.getSessionData();
```

## API 参考

### AuthService 类

| 方法                | 说明             | 返回值             |
| ------------------- | ---------------- | ------------------ |
| `autoLogin()`       | 自动登录         | `Promise<boolean>` |
| `isLoggedIn()`      | 检查登录状态     | `boolean`          |
| `getCurrentUser()`  | 获取当前用户信息 | `any`              |
| `logout()`          | 登出             | `void`             |
| `getSessionData()`  | 获取会话数据     | `any`              |
| `refreshUserInfo()` | 刷新用户信息     | `Promise<boolean>` |

### LoginHelper 类

| 方法                             | 说明           | 参数                         | 返回值             |
| -------------------------------- | -------------- | ---------------------------- | ------------------ |
| `initPageLogin(page, options)`   | 初始化页面登录 | `page: any, options: object` | `boolean`          |
| `manualLogin()`                  | 手动登录       | 无                           | `Promise<boolean>` |
| `registerUser(userInfo)`         | 用户注册       | `userInfo: object`           | `Promise<boolean>` |
| `logout()`                       | 登出           | 无                           | `void`             |
| `getLoginStatus()`               | 获取登录状态   | 无                           | `object`           |
| `checkLoginAndExecute(callback)` | 检查登录后执行 | `callback: function`         | `void`             |

## 配置项

### initPageLogin 选项

```typescript
{
  requireLogin?: boolean;           // 是否需要登录
  redirectUrl?: string;            // 未登录时跳转地址
  onLoginSuccess?: (userInfo) => void;    // 登录成功回调
  onLoginFailed?: () => void;             // 登录失败回调
  onNeedRegister?: (sessionData) => void; // 需要注册回调
}
```

## 注意事项

1. **Token 管理**: Token 会自动保存到本地存储，并在每次请求时自动添加到请求头
2. **事件监听**: 页面卸载时会自动移除事件监听，避免内存泄漏
3. **错误处理**: 所有网络请求都有统一的错误处理和用户提示
4. **登录状态**: 支持多页面同步登录状态变化
5. **用户注册**: 新用户需要完成注册流程才能正常使用

## 故障排除

### 常见问题

1. **登录失败**: 检查网络连接和后端接口状态
2. **Token 失效**: Token 会自动刷新，如果刷新失败会重新登录
3. **用户信息获取失败**: 可能是权限问题，检查小程序配置
4. **事件监听失效**: 确保正确调用 `initPageLogin` 方法

### 调试模式

在开发环境中，可以通过控制台查看详细的登录流程日志。
