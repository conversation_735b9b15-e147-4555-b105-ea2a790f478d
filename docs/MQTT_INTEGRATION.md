# MQTT 集成文档

## 概述

本项目已成功集成 MQTT 功能，用于实时监听电池状态变化。MQTT 服务订阅以下两个主题模式：

- `/sys/+/+/thing/property/post` - 设备属性上报
- `/sys/+/+/thing/event/post` - 设备事件上报

## 文件结构

```
├── services/
│   ├── mqtt.ts              # MQTT 核心服务类
│   └── mqttManager.ts       # MQTT 管理器（单例模式）
├── config/
│   └── mqtt.ts              # MQTT 配置文件
├── pages/
│   ├── tabbar/detail/       # 设备详情页（已集成 MQTT）
│   └── mqtt-test/           # MQTT 测试页面
└── docs/
    └── MQTT_INTEGRATION.md  # 本文档
```

## 核心功能

### 1. MQTT 服务类 (`services/mqtt.ts`)

- **MqttService**: 核心 MQTT 客户端服务
- **自动重连**: 支持断线重连机制
- **主题订阅**: 自动订阅设备属性和事件主题
- **消息解析**: 解析 MQTT 消息并触发相应事件

### 2. MQTT 管理器 (`services/mqttManager.ts`)

- **单例模式**: 全局唯一的 MQTT 管理实例
- **连接管理**: 统一管理 MQTT 连接状态
- **事件分发**: 将 MQTT 消息分发给不同的页面组件
- **错误处理**: 处理连接错误和重试逻辑

### 3. 配置管理 (`config/mqtt.ts`)

- **环境配置**: 支持开发环境和生产环境配置
- **连接参数**: 配置 MQTT 服务器地址、端口、认证信息等

## 使用方法

### 1. 配置 MQTT 服务器

编辑 `config/mqtt.ts` 文件，设置您的 MQTT 服务器信息：

```typescript
const productionConfig: MqttConfig = {
  host: 'your-mqtt-broker.com',     // 您的 MQTT 服务器地址
  port: 8084,                       // WebSocket SSL 端口
  protocol: 'wss',                  // 使用 WSS 协议
  username: 'your-username',        // 用户名
  password: 'your-password',        // 密码
  keepalive: 60,
  reconnectPeriod: 1000,
  connectTimeout: 30000,
};
```

### 2. 在页面中使用 MQTT

#### 初始化和监听设备数据

```typescript
import { mqttManager } from '../../services/mqttManager';
import type { DevicePropertyData, DeviceEventData } from '../../services/mqtt';

// 在页面 onLoad 中初始化
async onLoad() {
  // 初始化 MQTT 服务（如果尚未初始化）
  await mqttManager.initialize();
  
  // 设置设备数据监听
  this.setupDeviceDataListeners();
}

// 设置设备监听器
setupDeviceDataListeners() {
  const deviceId = 'your_device_id';
  
  // 监听设备属性更新
  mqttManager.onDevicePropertyUpdate(deviceId, (data: DevicePropertyData) => {
    console.log('设备属性更新:', data);
    // 更新页面数据
    this.updateDeviceProperties(data.properties);
  });
  
  // 监听设备事件
  mqttManager.onDeviceEvent(deviceId, (data: DeviceEventData) => {
    console.log('设备事件:', data);
    // 处理设备事件
    this.handleDeviceEvent(data);
  });
}

// 页面卸载时清理监听器
onUnload() {
  const deviceId = 'your_device_id';
  mqttManager.removeDeviceListeners(deviceId);
}
```

#### 处理设备数据更新

```typescript
// 处理设备属性更新
updateDeviceProperties(properties: Record<string, any>) {
  // 更新电池信息
  if (properties.batteryLevel !== undefined) {
    this.setData({
      'batteryInfo.level': properties.batteryLevel,
    });
  }
  
  // 更新电压数据
  if (properties.voltages && Array.isArray(properties.voltages)) {
    const voltageRows = this.formatVoltageData(properties.voltages);
    this.setData({ voltageRows });
  }
  
  // 更新设备状态
  this.setData({
    'deviceInfo.status': '在线',
    'deviceInfo.lastUpdate': new Date().toLocaleString(),
  });
}

// 处理设备事件
handleDeviceEvent(data: DeviceEventData) {
  const { eventType, eventData } = data;
  
  switch (eventType) {
    case 'alarm':
      // 处理报警事件
      wx.showModal({
        title: '设备报警',
        content: eventData.message || '设备发生异常',
        showCancel: false,
      });
      break;
    case 'status_change':
      // 处理状态变化事件
      this.setData({
        'deviceInfo.status': eventData.status,
      });
      break;
  }
}
```

### 3. 消息格式

#### 属性消息格式 (property/post)

```json
{
  "method": "thing.property.post",
  "id": "123456789",
  "params": {
    "batteryLevel": 85,
    "batteryStatus": "充电中",
    "voltages": [3518, 3520, 3515, 3522, 3518, 3519, 3517, 3521],
    "temperatures": [24.1, 24.3, 24.0, 24.2],
    "current": 12.5,
    "voltage": 28.1,
    "power": 351.25
  },
  "version": "1.0"
}
```

#### 事件消息格式 (event/post)

```json
{
  "method": "thing.event.post",
  "id": "123456789",
  "params": {
    "identifier": "alarm",
    "value": {
      "message": "电池温度过高",
      "level": "warning",
      "code": "TEMP_HIGH"
    },
    "time": 1640995200000
  },
  "version": "1.0"
}
```

## 测试功能

### MQTT 测试页面

访问 `pages/mqtt-test/mqtt-test` 页面可以测试 MQTT 功能：

1. **连接状态监控**: 查看 MQTT 连接状态、重试次数等
2. **手动连接**: 手动触发 MQTT 连接
3. **消息监听**: 实时查看接收到的 MQTT 消息
4. **测试消息**: 发送模拟的测试消息
5. **消息详情**: 点击消息查看详细内容

### 调试信息

在开发者工具的控制台中可以看到详细的 MQTT 调试信息：

- MQTT 连接状态变化
- 主题订阅情况
- 接收到的消息内容
- 错误信息和重连尝试

## 注意事项

### 1. 网络要求

- 微信小程序只支持 WSS (WebSocket Secure) 协议
- MQTT 服务器必须支持 WebSocket 连接
- 确保服务器域名已在微信小程序后台配置为合法域名

### 2. 连接管理

- MQTT 连接在应用启动时自动建立
- 支持断线自动重连，最多重试 5 次
- 页面切换不会断开 MQTT 连接，保持全局连接状态

### 3. 性能优化

- 消息列表最多保留 50 条记录，避免内存溢出
- 页面卸载时会清理相应的事件监听器
- 使用事件总线机制，避免直接耦合

### 4. 错误处理

- 连接失败时会显示错误提示
- 支持手动重连功能
- 消息解析失败不会影响其他功能

## 扩展功能

### 1. 添加新的消息类型

在 `services/mqtt.ts` 中的 `handleMessage` 方法中添加新的消息类型处理：

```typescript
if (topic.includes('/thing/custom/post')) {
  this.handleCustomMessage(deviceId, messageData);
}
```

### 2. 自定义事件监听

使用 `mqttManager.onGlobalPropertyUpdate` 和 `mqttManager.onGlobalEvent` 监听全局消息：

```typescript
// 监听所有设备的属性更新
mqttManager.onGlobalPropertyUpdate((data) => {
  console.log('全局属性更新:', data);
});

// 监听所有设备的事件
mqttManager.onGlobalEvent((data) => {
  console.log('全局事件:', data);
});
```

### 3. 消息过滤

可以在消息处理函数中添加过滤逻辑：

```typescript
handleDevicePropertyUpdate(data: DevicePropertyData) {
  // 只处理特定设备的消息
  if (data.deviceId !== this.data.currentDeviceId) {
    return;
  }
  
  // 只处理包含特定属性的消息
  if (!data.properties.batteryLevel) {
    return;
  }
  
  // 处理消息...
}
```

## 故障排除

### 1. 连接失败

- 检查 MQTT 服务器地址和端口是否正确
- 确认用户名和密码是否正确
- 检查网络连接是否正常
- 确认域名是否已在微信小程序后台配置

### 2. 消息接收不到

- 检查主题订阅是否成功
- 确认设备是否正在发送消息
- 检查消息格式是否符合预期
- 查看控制台是否有错误信息

### 3. 页面数据不更新

- 检查事件监听器是否正确设置
- 确认 `setData` 调用是否正确
- 检查数据格式转换是否有问题
- 查看控制台是否有 JavaScript 错误

如有其他问题，请查看控制台日志或联系开发团队。
