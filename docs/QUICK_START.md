# MQTT 集成快速开始指南

## 🚀 快速配置

### 1. 安装依赖

依赖已经添加到 `package.json` 中，运行以下命令安装：

```bash
npm install
```

### 2. 配置 MQTT 服务器

编辑 `config/mqtt.ts` 文件，替换为您的 MQTT 服务器信息：

```typescript
const productionConfig: MqttConfig = {
  host: 'your-mqtt-broker.com',     // 您的 MQTT 服务器地址
  port: 8084,                       // WebSocket SSL 端口
  protocol: 'wss',                  // 使用 WSS 协议
  username: 'your-username',        // 用户名
  password: 'your-password',        // 密码
};
```

### 3. 配置微信小程序域名

在微信小程序后台添加您的 MQTT 服务器域名到 `socket合法域名` 列表中。

## 📱 功能测试

### 1. 使用测试页面

访问 `pages/mqtt-test/mqtt-test` 页面进行功能测试：

1. 点击"连接MQTT"按钮
2. 查看连接状态
3. 点击"发送测试消息"模拟数据接收
4. 查看消息列表验证功能

### 2. 设备详情页面

在 `pages/tabbar/detail/detail` 页面可以看到：

- MQTT 连接状态指示器
- 实时数据更新
- 设备属性变化
- 事件通知

## 🔧 自定义配置

### 修改主题模式

在 `services/mqtt.ts` 中修改订阅的主题：

```typescript
private readonly PROPERTY_TOPIC_PATTERN = '/sys/+/+/thing/property/post';
private readonly EVENT_TOPIC_PATTERN = '/sys/+/+/thing/event/post';
```

### 添加新的消息处理

在 `handleMessage` 方法中添加新的消息类型处理：

```typescript
if (topic.includes('/your/custom/topic')) {
  this.handleCustomMessage(deviceId, messageData);
}
```

## 🐛 常见问题

### 连接失败
- 检查服务器地址和端口
- 确认用户名密码正确
- 检查域名是否已配置

### 消息接收不到
- 检查主题订阅是否成功
- 确认设备是否在发送消息
- 查看控制台错误信息

### 页面数据不更新
- 检查事件监听器设置
- 确认数据格式转换正确
- 查看 JavaScript 错误

## 📚 更多文档

- [完整集成文档](./MQTT_INTEGRATION.md)
- [API 参考](../api/README.md)

## 🎯 下一步

1. 根据您的实际需求修改消息处理逻辑
2. 添加更多的设备状态监控
3. 实现消息持久化存储
4. 添加离线消息处理

如有问题，请查看控制台日志或联系开发团队。
