# API 接口文档

本目录包含根据接口文档自动生成的 API 请求文件，适配微信小程序开发框架。

## 文件结构

```
api/
├── types.ts          # TypeScript 类型定义
├── auth.ts           # 认证相关接口
├── user.ts           # 用户相关接口
├── device.ts         # 设备相关接口
├── captcha.ts        # 验证码相关接口
├── storage.ts        # 对象存储相关接口
├── index.ts          # 统一导出文件
├── example.ts        # 使用示例
└── README.md         # 使用说明
```

## 快速开始

### 1. 导入 API

```typescript
// 导入特定模块
import { authApi, userApi, deviceApi, captchaApi, storageApi } from '@/api';

// 或者导入单个函数
import {
  login,
  loginByMobile,
  getMe,
  getDeviceBySn,
  getUserPhoneNumber,
  getCaptchaByMobile,
  verifyCaptcha,
  uploadObject,
  uploadImage,
  getObject,
} from '@/api';

// 导入类型定义
import type {
  WxmpUser,
  Device,
  CreateUserRequest,
  UpdateUserRequest,
  GetPhoneNumberResult,
} from '@/api';
```

### 2. 基本使用

#### 登录流程

```typescript
// 小程序登录
async function handleLogin() {
  try {
    const loginRes = await wx.login();
    const result = await authApi.login({ code: loginRes.code });
    console.log('登录成功:', result);
  } catch (error) {
    console.error('登录失败:', error);
  }
}

// 手机号验证码登录
async function handleMobileLogin(mobile: string) {
  try {
    // 1. 获取验证码
    const captchaResult = await captchaApi.getCaptchaByMobile({ mobile });
    const captchaKey = captchaResult.data;

    // 2. 用户输入验证码后，进行登录
    const code = '1234'; // 用户输入的验证码
    const loginResult = await authApi.loginByMobile({ key: captchaKey, code });
    console.log('手机号登录成功:', loginResult);
  } catch (error) {
    console.error('手机号登录失败:', error);
  }
}
```

#### 获取用户信息

```typescript
// 获取当前用户信息
async function getCurrentUser() {
  try {
    const user = await userApi.getMe();
    return user.data;
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
}
```

#### 更新用户信息

```typescript
// 更新用户信息
async function updateUserProfile(data: UpdateUserRequest) {
  try {
    const result = await userApi.updateMe(data);
    console.log('更新成功:', result);
    return result;
  } catch (error) {
    console.error('更新用户信息失败:', error);
  }
}
```

#### 对象存储

```typescript
// 上传图片
async function uploadUserAvatar(filePath: string) {
  try {
    const result = await storageApi.uploadImage(
      'avatars/user123.jpg',
      filePath
    );
    console.log('上传成功:', result);
    return result;
  } catch (error) {
    console.error('上传失败:', error);
  }
}

// 获取文件
async function downloadFile(key: string) {
  try {
    const fileData = await storageApi.getObject(key);
    console.log('文件下载成功:', fileData);
    return fileData;
  } catch (error) {
    console.error('文件下载失败:', error);
  }
}
```

#### 获取微信手机号

```typescript
// 获取微信用户手机号
async function getPhoneNumber(phoneCode: string) {
  try {
    const result = await userApi.getUserPhoneNumber({ code: phoneCode });
    console.log('手机号信息:', result.data);
    return result.data.phoneNumber;
  } catch (error) {
    console.error('获取手机号失败:', error);
  }
}

// 在小程序页面中使用
Page({
  // 获取手机号按钮事件
  async getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      const phoneNumber = await getPhoneNumber(e.detail.code);
      if (phoneNumber) {
        this.setData({ mobile: phoneNumber });
      }
    }
  },
});
```

#### 创建用户

```typescript
// 创建新用户
async function createUser(userData: CreateUserRequest) {
  try {
    const result = await userApi.createUser(userData);
    return result.data;
  } catch (error) {
    console.error('创建用户失败:', error);
  }
}
```

#### 设备操作

```typescript
// 获取当前用户设备列表
async function getUserDevices() {
  try {
    const devices = await userApi.getUserDevices();
    return devices.data;
  } catch (error) {
    console.error('获取设备列表失败:', error);
  }
}

// 根据序列号查询设备
async function getDevice(sn: string) {
  try {
    const device = await deviceApi.getDeviceBySn(sn);
    return device.data;
  } catch (error) {
    console.error('获取设备信息失败:', error);
  }
}
```

## API 列表

### 认证接口 (auth.ts)

| 函数名         | 描述       | 参数               | 返回类型                                                     |
| -------------- | ---------- | ------------------ | ------------------------------------------------------------ |
| `code2Session` | 小程序登录 | `{ code: string }` | `Promise<BaseResponse<Code2SessionResponse>>`                |
| `login`        | 用户登录   | `{ code: string }` | `Promise<BaseResponse<AccessToken \| Code2SessionResponse>>` |

### 用户接口 (user.ts)

| 函数名               | 描述                 | 参数                | 返回类型                                      |
| -------------------- | -------------------- | ------------------- | --------------------------------------------- |
| `getMe`              | 获取当前用户信息     | 无                  | `Promise<BaseResponse<WxmpUser>>`             |
| `updateMe`           | 更新用户信息         | `UpdateUserRequest` | `Promise<BaseResponse>`                       |
| `getUser`            | 获取用户信息         | `GetUserParams?`    | `Promise<BaseResponse<WxmpUser>>`             |
| `createUser`         | 创建用户             | `CreateUserRequest` | `Promise<BaseResponse<WxmpUser>>`             |
| `getUserDevices`     | 获取当前用户设备列表 | 无                  | `Promise<BaseResponse<Device[]>>`             |
| `getUserPhoneNumber` | 获取微信用户手机号   | `{ code: string }`  | `Promise<BaseResponse<GetPhoneNumberResult>>` |

### 设备接口 (device.ts)

| 函数名          | 描述               | 参数         | 返回类型                        |
| --------------- | ------------------ | ------------ | ------------------------------- |
| `getDeviceBySn` | 根据序列号查询设备 | `sn: string` | `Promise<BaseResponse<Device>>` |

### 验证码接口 (captcha.ts)

| 函数名               | 描述             | 参数                            | 返回类型                        |
| -------------------- | ---------------- | ------------------------------- | ------------------------------- |
| `getCaptchaByMobile` | 获取手机号验证码 | `{ mobile: string }`            | `Promise<BaseResponse<string>>` |
| `verifyCaptcha`      | 验证手机号验证码 | `{ key: string, code: string }` | `Promise<BaseResponse>`         |

### 对象存储接口 (storage.ts)

| 函数名         | 描述         | 参数                                       | 返回类型                |
| -------------- | ------------ | ------------------------------------------ | ----------------------- |
| `uploadObject` | 上传对象     | `key: string, file: ArrayBuffer \| string` | `Promise<BaseResponse>` |
| `getObject`    | 获取对象     | `key: string`                              | `Promise<ArrayBuffer>`  |
| `uploadImage`  | 上传图片文件 | `key: string, filePath: string`            | `Promise<BaseResponse>` |

## 类型定义

### 基础类型

```typescript
interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

interface ErrorResponse {
  code: number;
  message: string;
  detail: string;
}
```

### 认证相关类型

```typescript
interface AccessToken {
  accessToken: string; // 更新：字段名从 value 改为 accessToken
  expireAt: number;
}

interface Code2SessionResponse {
  openid: string;
  unionid: string;
}
```

### 用户相关类型

```typescript
interface WxmpUser {
  id: string;
  wx_openid: string;
  wx_unionid: string;
  nickname: string;
  avatar: string;
  mobile: string;
  user_id?: string;
  edges?: {
    user?: User;
    devices?: Device[];
  };
}

interface CreateUserRequest {
  wx_openid: string;
  wx_unionid: string;
  nickname: string;
  avatar: string;
  mobile: string;
}

interface UpdateUserRequest {
  nickname?: string;
  avatar?: string;
  mobile?: string;
}

interface GetPhoneNumberResult {
  phoneNumber: string;
  purePhoneNumber: string;
  countryCode: string;
  watermark: {
    timestamp: number;
    appid: string;
  };
}
```

### 设备相关类型

```typescript
interface Device {
  id: string;
  sn: string;
  name?: string;
  properties: Record<string, any>;
  online: boolean;
  uploadTime: number;
  activeTime: number;
  createdAt: number;
  updatedAt: number;
  description?: string;
  productId: string;
  edges?: {
    product?: Product;
  };
}
```

## 实际使用示例

### 完整的用户注册流程

```typescript
// 在注册页面中
Page({
  data: {
    userInfo: null,
    mobile: '',
  },

  // 获取用户基本信息
  async getUserProfile() {
    try {
      const res = await wx.getUserProfile({
        desc: '用于完善用户资料',
      });
      this.setData({
        userInfo: res.userInfo,
      });
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  // 获取手机号
  async getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      try {
        const result = await getUserPhoneNumber({ code: e.detail.code });
        if (result && result.data) {
          this.setData({
            mobile: result.data.phoneNumber,
          });
        }
      } catch (error) {
        console.error('获取手机号失败:', error);
      }
    }
  },

  // 提交注册
  async submitRegister() {
    const { userInfo, mobile } = this.data;
    if (!userInfo || !mobile) {
      wx.showToast({ title: '请完善信息', icon: 'none' });
      return;
    }

    try {
      // 获取会话数据
      const sessionData = authService.getSessionData();

      // 创建用户
      const result = await createUser({
        wx_openid: sessionData.openid,
        wx_unionid: sessionData.unionid,
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl,
        mobile: mobile,
      });

      if (result) {
        wx.showToast({ title: '注册成功', icon: 'success' });
        // 注册成功后重新登录
        const app = getApp();
        await app.login();
      }
    } catch (error) {
      console.error('注册失败:', error);
      wx.showToast({ title: '注册失败', icon: 'none' });
    }
  },
});
```

### 用户信息管理

```typescript
// 个人中心页面
Page({
  data: {
    userInfo: null,
  },

  async onLoad() {
    await this.loadUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const result = await getMe();
      if (result && result.data) {
        this.setData({
          userInfo: result.data,
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  // 更新用户信息
  async updateUserInfo(updateData) {
    try {
      await updateMe(updateData);
      wx.showToast({ title: '更新成功', icon: 'success' });
      // 重新加载用户信息
      await this.loadUserInfo();
    } catch (error) {
      console.error('更新失败:', error);
      wx.showToast({ title: '更新失败', icon: 'none' });
    }
  },
});
```

## 错误处理

所有 API 函数都会返回 Promise，建议使用 try-catch 进行错误处理：

```typescript
async function handleApiCall() {
  try {
    const result = await userApi.getMe();
    // 处理成功结果
    console.log(result.data);
  } catch (error) {
    // 处理错误
    console.error('API调用失败:', error);

    // 显示用户友好的错误信息
    wx.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    });
  }
}
```

## 更新日志

### 最新更新 (2024-06-30)

1. **新增验证码模块**:
   - `getCaptchaByMobile()` - 获取手机号验证码
   - `verifyCaptcha()` - 验证手机号验证码
2. **新增对象存储模块**:
   - `uploadObject()` - 通用对象上传
   - `getObject()` - 获取对象文件（返回二进制数据）
   - `uploadImage()` - 专门的图片上传函数
3. **新增手机号登录**:
   - `loginByMobile()` - 支持手机号验证码登录
4. **新增类型定义**:
   - `Mobile` - 手机号类型（11 位格式验证）
   - `CaptchaCode` - 验证码类型（4 位数字）
   - `MobileCaptchaVerifyRequest` - 手机号验证码验证请求
   - `BindDeviceRequest` - 设备绑定请求
   - `UploadObjectParams` - 对象上传参数
   - `GetObjectParams` - 对象获取参数
5. **新增设备绑定功能**:
   - `bindDevice()` - 绑定设备到当前用户

### 历史更新 (2024-12-28)

1. **AccessToken 字段更新**: `value` 字段改为 `accessToken`
2. **接口方法更新**:
   - `updateMe()` - 更新用户信息（请求方法从 POST 改为 PATCH）
   - `getUserDevices()` - 获取用户设备列表（接口路径从 `/v1/user/devices` 改为 `/v1/me/devices`）
3. **新增接口**:
   - `getUserPhoneNumber()` - 获取微信用户手机号
4. **新增类型**:
   - `UpdateUserRequest` - 用户信息更新请求
   - `GetPhoneNumberResult` - 手机号获取结果
   - `GetPhoneNumberParams` - 手机号获取参数
5. **类型定义修复**:
   - `GetPhoneNumberResult.countryCode` 字段类型从 `number` 改为 `string`

## 注意事项

1. **认证**: 大部分接口需要用户登录后才能调用，请确保已经调用登录接口并获取到 token
2. **错误处理**: 建议对所有 API 调用进行错误处理，提供良好的用户体验
3. **类型安全**: 使用 TypeScript 类型定义确保代码的类型安全
4. **网络状态**: 在网络不佳的环境下，建议添加重试机制
5. **手机号获取**: 获取微信手机号需要用户主动触发（如点击按钮），不能自动获取
6. **验证码**: 验证码有效期限制，请及时使用；验证码 key 长度固定为 20 位
7. **手机号格式**: 手机号必须符合格式 `^1[3-9]\d{9}$`（11 位中国大陆手机号）
8. **对象存储**:
   - 上传的文件 key 支持目录结构（如 `folder/file.ext`）
   - 获取对象返回的是二进制数据，需要根据文件类型进行相应处理
   - 上传文件需要认证，请确保已登录
9. **设备绑定**: 设备序列号长度必须在 16-20 位之间

## 配置

API 的基础配置在 `utils/request.ts` 和 `config/index.ts` 中，包括：

- 基础 URL 配置
- 请求头设置
- Token 管理
- 错误处理

如需修改配置，请参考这些文件的实现。
