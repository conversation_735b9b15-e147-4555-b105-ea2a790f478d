import { Restful } from '../utils/request';
import config from '../config/index';
import type {
  BaseResponse,
  UploadObjectParams,
  GetObjectParams,
} from './types';

const request = new Restful();

/**
 * 上传对象到存储
 * @param key 文件路径，支持目录结构，例如：test/test.txt
 * @param file 要上传的文件数据
 * @returns Promise<BaseResponse>
 */
export function uploadObject(
  key: string,
  file: ArrayBuffer | string
): Promise<BaseResponse> {
  return request.uploadFile(`/v1/object/${encodeURIComponent(key)}`, {
    filePath: file,
    name: 'file',
    formData: {},
  });
}

/**
 * 获取存储的对象
 * @param key 文件路径
 * @returns Promise<ArrayBuffer> 返回文件的二进制数据
 */
export function getObject(key: string): Promise<ArrayBuffer> {
  // 注意：这个接口返回的是二进制数据，不是标准的 JSON 响应格式
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${config.host}/v1/object/${encodeURIComponent(key)}`,
      method: 'GET',
      header: {
        Authorization: `Bearer ${wx.getStorageSync('access_token')}`,
      },
      responseType: 'arraybuffer',
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data as ArrayBuffer);
        } else {
          reject(new Error(`HTTP ${res.statusCode}`));
        }
      },
      fail: reject,
    });
  });
}

/**
 * 上传图片文件
 * @param key 文件路径
 * @param filePath 本地文件路径
 * @returns Promise<BaseResponse>
 */
export function uploadImage(
  key: string,
  filePath: string
): Promise<BaseResponse> {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: `${config.host}/v1/object/${encodeURIComponent(key)}`,
      filePath,
      name: 'file',
      header: {
        Authorization: `Bearer ${wx.getStorageSync('access_token')}`,
      },
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data);
            resolve(data);
          } catch (e) {
            resolve({ code: 0, message: 'success', data: res.data });
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
        }
      },
      fail: reject,
    });
  });
}

export default {
  uploadObject,
  getObject,
  uploadImage,
};
