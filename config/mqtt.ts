import { MqttConfig } from '../services/mqtt';

// MQTT配置 - 开发环境
const developmentConfig: MqttConfig = {
  host: 'localhost', // 替换为您的MQTT服务器地址
  port: 8083, // WebSocket端口
  protocol: 'ws',
  username: '', // 替换为您的用户名
  password: '', // 替换为您的密码
  keepalive: 60,
  reconnectPeriod: 1000,
  connectTimeout: 30000,
};

// MQTT配置 - 生产环境
const productionConfig: MqttConfig = {
  host: 'your-mqtt-broker.com', // 替换为您的生产环境MQTT服务器地址
  port: 8084, // WebSocket SSL端口
  protocol: 'wss',
  username: '', // 替换为您的用户名
  password: '', // 替换为您的密码
  keepalive: 60,
  reconnectPeriod: 1000,
  connectTimeout: 30000,
};

// 根据环境获取配置
function getMqttConfig(): MqttConfig {
  // 这里可以根据环境变量或其他方式判断环境
  const isDevelopment = true; // 可以根据实际情况修改
  
  return isDevelopment ? developmentConfig : productionConfig;
}

export { getMqttConfig };
export default getMqttConfig();
