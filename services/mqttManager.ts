import { MqttService, initMqttService, getMqttService, destroyMqttService } from './mqtt';
import { getMqttConfig } from '../config/mqtt';
import { eventBus } from '../utils/eventBus';

/**
 * MQTT管理器 - 统一管理MQTT连接和订阅
 */
export class MqttManager {
  private static instance: MqttManager | null = null;
  private mqttService: MqttService | null = null;
  private isInitialized: boolean = false;
  private connectionRetryCount: number = 0;
  private maxRetryCount: number = 5;
  private retryTimer: number | null = null;

  private constructor() {
    // 私有构造函数，实现单例模式
  }

  /**
   * 获取单例实例
   */
  static getInstance(): MqttManager {
    if (!MqttManager.instance) {
      MqttManager.instance = new MqttManager();
    }
    return MqttManager.instance;
  }

  /**
   * 初始化MQTT服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('MQTT服务已初始化');
      return;
    }

    try {
      console.log('初始化MQTT服务...');
      
      // 获取配置
      const config = getMqttConfig();
      
      // 创建MQTT服务实例
      this.mqttService = initMqttService(config);
      
      // 设置事件监听
      this.setupEventListeners();
      
      // 连接MQTT服务器
      await this.mqttService.connect();
      
      this.isInitialized = true;
      this.connectionRetryCount = 0;
      
      console.log('MQTT服务初始化成功');
      
    } catch (error) {
      console.error('MQTT服务初始化失败:', error);
      this.handleConnectionError();
      throw error;
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // MQTT连接成功
    eventBus.on('mqttConnected', () => {
      console.log('MQTT连接成功事件');
      this.connectionRetryCount = 0;
      if (this.retryTimer) {
        clearTimeout(this.retryTimer);
        this.retryTimer = null;
      }
    });

    // MQTT连接错误
    eventBus.on('mqttError', (data: { error: any }) => {
      console.error('MQTT连接错误事件:', data.error);
      this.handleConnectionError();
    });

    // MQTT连接断开
    eventBus.on('mqttDisconnected', () => {
      console.log('MQTT连接断开事件');
      this.handleConnectionError();
    });

    // MQTT重连中
    eventBus.on('mqttReconnecting', () => {
      console.log('MQTT重连中...');
    });
  }

  /**
   * 处理连接错误
   */
  private handleConnectionError(): void {
    if (this.connectionRetryCount >= this.maxRetryCount) {
      console.error('MQTT连接重试次数已达上限，停止重试');
      return;
    }

    this.connectionRetryCount++;
    const retryDelay = Math.min(1000 * Math.pow(2, this.connectionRetryCount), 30000); // 指数退避，最大30秒

    console.log(`MQTT连接失败，${retryDelay}ms后进行第${this.connectionRetryCount}次重试`);

    this.retryTimer = setTimeout(() => {
      this.reconnect();
    }, retryDelay);
  }

  /**
   * 重新连接
   */
  async reconnect(): Promise<void> {
    try {
      if (this.mqttService) {
        await this.mqttService.connect();
      } else {
        await this.initialize();
      }
    } catch (error) {
      console.error('MQTT重连失败:', error);
      this.handleConnectionError();
    }
  }

  /**
   * 手动重连
   */
  async manualReconnect(): Promise<void> {
    this.connectionRetryCount = 0;
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }
    await this.reconnect();
  }

  /**
   * 获取连接状态
   */
  isConnected(): boolean {
    return this.mqttService?.getConnectionStatus() || false;
  }

  /**
   * 获取已订阅的主题
   */
  getSubscribedTopics(): string[] {
    return this.mqttService?.getSubscribedTopics() || [];
  }

  /**
   * 订阅特定设备的主题
   */
  subscribeDeviceTopics(productKey: string, deviceName: string): void {
    if (!this.mqttService || !this.isConnected()) {
      console.warn('MQTT服务未连接，无法订阅设备主题');
      return;
    }

    const propertyTopic = `/sys/${productKey}/${deviceName}/thing/property/post`;
    const eventTopic = `/sys/${productKey}/${deviceName}/thing/event/post`;

    console.log(`订阅设备主题: ${propertyTopic}, ${eventTopic}`);
    
    // 这里可以扩展为支持特定设备的主题订阅
    // 当前实现已经通过通配符订阅了所有设备
  }

  /**
   * 监听设备属性更新
   */
  onDevicePropertyUpdate(deviceId: string, callback: (data: any) => void): void {
    eventBus.on(`devicePropertyUpdate_${deviceId}`, callback);
  }

  /**
   * 监听设备事件
   */
  onDeviceEvent(deviceId: string, callback: (data: any) => void): void {
    eventBus.on(`deviceEvent_${deviceId}`, callback);
  }

  /**
   * 移除设备监听器
   */
  removeDeviceListeners(deviceId: string): void {
    eventBus.off(`devicePropertyUpdate_${deviceId}`);
    eventBus.off(`deviceEvent_${deviceId}`);
  }

  /**
   * 监听全局设备属性更新
   */
  onGlobalPropertyUpdate(callback: (data: any) => void): void {
    eventBus.on('devicePropertyUpdate', callback);
  }

  /**
   * 监听全局设备事件
   */
  onGlobalEvent(callback: (data: any) => void): void {
    eventBus.on('deviceEvent', callback);
  }

  /**
   * 销毁MQTT服务
   */
  destroy(): void {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }

    // 移除所有事件监听器
    eventBus.off('mqttConnected');
    eventBus.off('mqttError');
    eventBus.off('mqttDisconnected');
    eventBus.off('mqttReconnecting');

    // 销毁MQTT服务
    destroyMqttService();
    this.mqttService = null;
    this.isInitialized = false;
    this.connectionRetryCount = 0;

    console.log('MQTT服务已销毁');
  }

  /**
   * 获取连接统计信息
   */
  getConnectionStats(): {
    isConnected: boolean;
    retryCount: number;
    subscribedTopics: string[];
  } {
    return {
      isConnected: this.isConnected(),
      retryCount: this.connectionRetryCount,
      subscribedTopics: this.getSubscribedTopics(),
    };
  }
}

// 导出单例实例
export const mqttManager = MqttManager.getInstance();
