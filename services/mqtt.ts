import * as mqtt from 'mqtt';
import { eventBus } from '../utils/eventBus';

// MQTT消息类型定义
export interface MqttPropertyMessage {
  method: string;
  id: string;
  params: Record<string, any>;
  version: string;
}

export interface MqttEventMessage {
  method: string;
  id: string;
  params: {
    identifier: string;
    value: Record<string, any>;
    time: number;
  };
  version: string;
}

export interface MqttConfig {
  host: string;
  port: number;
  protocol: 'ws' | 'wss';
  username?: string;
  password?: string;
  clientId?: string;
  keepalive?: number;
  reconnectPeriod?: number;
  connectTimeout?: number;
}

export interface DevicePropertyData {
  deviceId: string;
  properties: Record<string, any>;
  timestamp: number;
}

export interface DeviceEventData {
  deviceId: string;
  eventType: string;
  eventData: Record<string, any>;
  timestamp: number;
}

/**
 * MQTT服务类 - 用于处理设备状态的实时监听
 */
export class MqttService {
  private client: mqtt.MqttClient | null = null;
  private config: MqttConfig;
  private isConnected: boolean = false;
  private subscribedTopics: Set<string> = new Set();
  
  // 主题模式
  private readonly PROPERTY_TOPIC_PATTERN = '/sys/+/+/thing/property/post';
  private readonly EVENT_TOPIC_PATTERN = '/sys/+/+/thing/event/post';

  constructor(config: MqttConfig) {
    this.config = {
      keepalive: 60,
      reconnectPeriod: 1000,
      connectTimeout: 30000,
      clientId: `wxmp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...config,
    };
  }

  /**
   * 连接MQTT服务器
   */
  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const brokerUrl = `${this.config.protocol}://${this.config.host}:${this.config.port}`;
        
        console.log('正在连接MQTT服务器:', brokerUrl);
        
        this.client = mqtt.connect(brokerUrl, {
          clientId: this.config.clientId,
          username: this.config.username,
          password: this.config.password,
          keepalive: this.config.keepalive,
          reconnectPeriod: this.config.reconnectPeriod,
          connectTimeout: this.config.connectTimeout,
          clean: true,
        });

        this.client.on('connect', () => {
          console.log('MQTT连接成功');
          this.isConnected = true;
          this.subscribeToTopics();
          eventBus.emit('mqttConnected');
          resolve();
        });

        this.client.on('error', (error) => {
          console.error('MQTT连接错误:', error);
          this.isConnected = false;
          eventBus.emit('mqttError', { error });
          reject(error);
        });

        this.client.on('close', () => {
          console.log('MQTT连接关闭');
          this.isConnected = false;
          eventBus.emit('mqttDisconnected');
        });

        this.client.on('reconnect', () => {
          console.log('MQTT正在重连...');
          eventBus.emit('mqttReconnecting');
        });

        this.client.on('message', (topic, message) => {
          this.handleMessage(topic, message);
        });

      } catch (error) {
        console.error('MQTT连接初始化失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 订阅主题
   */
  private subscribeToTopics(): void {
    if (!this.client || !this.isConnected) {
      console.warn('MQTT客户端未连接，无法订阅主题');
      return;
    }

    const topics = [
      this.PROPERTY_TOPIC_PATTERN,
      this.EVENT_TOPIC_PATTERN,
    ];

    topics.forEach(topic => {
      this.client!.subscribe(topic, { qos: 1 }, (error) => {
        if (error) {
          console.error(`订阅主题失败 ${topic}:`, error);
        } else {
          console.log(`成功订阅主题: ${topic}`);
          this.subscribedTopics.add(topic);
        }
      });
    });
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(topic: string, message: Buffer): void {
    try {
      const messageStr = message.toString();
      console.log(`收到MQTT消息 [${topic}]:`, messageStr);

      // 解析主题获取设备信息
      const topicParts = topic.split('/');
      if (topicParts.length < 6) {
        console.warn('主题格式不正确:', topic);
        return;
      }

      const productKey = topicParts[2];
      const deviceName = topicParts[3];
      const deviceId = `${productKey}_${deviceName}`;

      // 解析消息内容
      const messageData = JSON.parse(messageStr);

      if (topic.includes('/thing/property/post')) {
        this.handlePropertyMessage(deviceId, messageData);
      } else if (topic.includes('/thing/event/post')) {
        this.handleEventMessage(deviceId, messageData);
      }

    } catch (error) {
      console.error('处理MQTT消息失败:', error);
    }
  }

  /**
   * 处理属性消息
   */
  private handlePropertyMessage(deviceId: string, message: MqttPropertyMessage): void {
    const propertyData: DevicePropertyData = {
      deviceId,
      properties: message.params || {},
      timestamp: Date.now(),
    };

    console.log('设备属性更新:', propertyData);
    
    // 发送事件通知
    eventBus.emit('devicePropertyUpdate', propertyData);
    eventBus.emit(`devicePropertyUpdate_${deviceId}`, propertyData);
  }

  /**
   * 处理事件消息
   */
  private handleEventMessage(deviceId: string, message: MqttEventMessage): void {
    const eventData: DeviceEventData = {
      deviceId,
      eventType: message.params?.identifier || 'unknown',
      eventData: message.params?.value || {},
      timestamp: message.params?.time || Date.now(),
    };

    console.log('设备事件:', eventData);
    
    // 发送事件通知
    eventBus.emit('deviceEvent', eventData);
    eventBus.emit(`deviceEvent_${deviceId}`, eventData);
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.client) {
      console.log('断开MQTT连接');
      this.client.end();
      this.client = null;
      this.isConnected = false;
      this.subscribedTopics.clear();
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * 获取已订阅的主题
   */
  getSubscribedTopics(): string[] {
    return Array.from(this.subscribedTopics);
  }

  /**
   * 手动重连
   */
  reconnect(): void {
    if (this.client) {
      console.log('手动重连MQTT');
      this.client.reconnect();
    }
  }
}

// 创建全局MQTT服务实例
let mqttService: MqttService | null = null;

/**
 * 初始化MQTT服务
 */
export function initMqttService(config: MqttConfig): MqttService {
  if (mqttService) {
    mqttService.disconnect();
  }
  
  mqttService = new MqttService(config);
  return mqttService;
}

/**
 * 获取MQTT服务实例
 */
export function getMqttService(): MqttService | null {
  return mqttService;
}

/**
 * 销毁MQTT服务
 */
export function destroyMqttService(): void {
  if (mqttService) {
    mqttService.disconnect();
    mqttService = null;
  }
}
