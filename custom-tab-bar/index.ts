import TabMenu from './data';
import { getDeviceBySn } from '../api/device';

// 定义扫码数据类型
interface ScanData {
  sn: string;
  [key: string]: any;
}

Component({
  data: {
    active: 0,
    list: TabMenu,
    // 扫码确认弹窗相关
    showScanConfirmPopup: false,
    scannedDeviceInfo: {
      serialNumber: '',
      firmwareVersion: '',
      hardwareVersion: '',
      rawScanData: '',
    },
  },

  attached() {
    console.log('TabBar组件attached');
    this.init();
  },

  methods: {
    onChange(event: any) {
      const index = event.detail.value;
      const item = this.data.list[index];

      // console.log('TabBar切换:', index, item);

      // 如果是加号按钮，执行特殊逻辑
      if (item.isAddButton) {
        this.handleAddButtonClick();
        return;
      }

      const url = item.url;

      // 立即更新UI状态，提供即时视觉反馈
      this.setData({ active: index });

      // 页面切换
      wx.switchTab({
        url: url.startsWith('/') ? url : `/${url}`,
        success: () => {
          // console.log('Tab切换成功:', url);
          // 切换成功后确保状态正确
          this.setData({ active: index });
        },
        fail: (err: any) => {
          // console.error('Tab切换失败:', err);
          // 切换失败时恢复之前的状态
          this.init();
        },
      });
    },

    // 处理加号按钮点击 - 直接打开扫一扫
    handleAddButtonClick() {
      console.log('加号按钮被点击，打开扫一扫');
      this.onScan();
    },

    // 扫码功能
    onScan() {
      wx.scanCode({
        onlyFromCamera: true,
        success: (res: any) => {
          console.log('扫码结果:', res);
          const scannedData = res.result;
          console.log('扫码数据:', scannedData);

          // 模拟设备识别过程
          this.recognizeDevice(scannedData);
        },
        fail: (err: any) => {
          console.error('扫码失败:', err);
          // wx.showToast({
          //   title: '扫码失败',
          //   icon: 'none',
          // });
        },
      });
    },

    // 设备识别
    async recognizeDevice(scanData: string) {
      wx.showLoading({
        title: '识别设备中...',
        mask: true,
      });

      try {
        // 解析扫码数据
        const parsedScanData: ScanData = JSON.parse(scanData);

        // 通过API验证设备是否存在
        const res = await getDeviceBySn(parsedScanData.sn);
        console.log('设备信息:', res);

        if (res) {
          // 设备识别成功
          const deviceInfo = {
            serialNumber: parsedScanData.sn,
            firmwareVersion: res?.properties?.FirmwareVersion
              ? `V ${res.properties.FirmwareVersion}`
              : '',
            hardwareVersion: res?.properties?.FirmwareVersion
              ? `V ${res.properties.FirmwareVersion}`
              : '',
            rawScanData: JSON.stringify(parsedScanData),
          };

          // 更新数据并显示确认弹窗
          this.setData({
            scannedDeviceInfo: deviceInfo,
            showScanConfirmPopup: true,
          });
        } else {
          // 设备不存在或获取失败
          throw new Error(res.message || '设备不存在');
        }
      } catch (error: any) {
        console.error('设备识别失败:', error);
        wx.showToast({
          title: error.message || '设备识别失败',
          icon: 'none',
        });
      } finally {
        wx.hideLoading();
      }
    },

    // 确认设备 - 跳转到设备信息页面
    onConfirmDevice() {
      const { scannedDeviceInfo } = this.data;

      this.setData({
        showScanConfirmPopup: false,
      });

      // 跳转到扫码设备信息页面，让用户进行绑定操作
      wx.navigateTo({
        url: `/pages/scan-device-info/scan-device-info?deviceId=${scannedDeviceInfo.serialNumber}`,
      });
    },

    // 取消确认
    onCancelDevice() {
      this.setData({
        showScanConfirmPopup: false,
      });
    },

    // 弹窗可见性变化
    onScanPopupVisibleChange(e: any) {
      this.setData({
        showScanConfirmPopup: e.detail.visible,
      });
    },

    init() {
      const page = getCurrentPages().pop();
      if (!page) {
        // console.warn('TabBar init: 无法获取当前页面');
        return;
      }

      const route = page.route.split('?')[0];
      // console.log('TabBar init: 当前路由', route);

      const active = this.data.list.findIndex((item: any) => {
        // 跳过加号按钮
        if (item.isAddButton) {
          return false;
        }
        const itemUrl = item.url.startsWith('/')
          ? item.url.substring(1)
          : item.url;
        // console.log('TabBar init: 比较路由', itemUrl, 'vs', route);
        return itemUrl === route;
      });

      console.log('TabBar init: 计算得到的active索引', active);

      if (active !== -1) {
        if (active !== this.data.active) {
          // console.log(
          //   'TabBar init: 更新active状态',
          //   this.data.active,
          //   '->',
          //   active
          // );
          this.setData({ active });
        } else {
          // console.log('TabBar init: active状态无需更新');
        }
      } else {
        // console.warn('TabBar init: 未找到匹配的路由', route);
      }
    },

    // 提供外部调用的方法来更新活跃状态
    updateActiveTab(index: number) {
      // console.log('TabBar updateActiveTab:', index);
      if (index >= 0 && index < this.data.list.length) {
        this.setData({ active: index });
      }
    },

    // 强制刷新状态
    forceRefresh() {
      // console.log('TabBar forceRefresh');
      // 延迟执行，确保页面切换完成
      setTimeout(() => {
        this.init();
      }, 100);
    },
  },
});
