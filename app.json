{"pages": ["pages/tabbar/index/index", "pages/device-list/device-list", "pages/tabbar/mall/mall", "pages/tabbar/mine/mine", "pages/tabbar/detail/detail", "pages/charging-record/charging-record", "pages/profile-edit/profile-edit", "pages/schedule-heat/schedule-heat", "pages/bluetooth-search/bluetooth-search", "pages/bluetooth-connect/bluetooth-connect", "pages/scan-device-info/scan-device-info", "pages/bind-device/bind-device", "pages/tabbar/add/add", "pages/mqtt-test/mqtt-test"], "tabBar": {"custom": true, "color": "#666666", "selectedColor": "#FF5F15", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/tabbar/index/index", "text": "首页"}, {"pagePath": "pages/device-list/device-list", "text": "设备"}, {"pagePath": "pages/tabbar/add/add", "text": ""}, {"pagePath": "pages/tabbar/mall/mall", "text": "商城"}, {"pagePath": "pages/tabbar/mine/mine", "text": "我的"}]}, "window": {"navigationBarTextStyle": "black", "navigationStyle": "custom"}, "rendererOptions": {"skyline": {"defaultDisplayBlock": true, "disableABTest": true, "sdkVersionBegin": "3.0.0", "sdkVersionEnd": "15.255.255"}}, "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["getLocation", "onLocationChange", "startLocationUpdateBackground", "<PERSON><PERSON><PERSON><PERSON>"], "lazyCodeLoading": "requiredComponents"}